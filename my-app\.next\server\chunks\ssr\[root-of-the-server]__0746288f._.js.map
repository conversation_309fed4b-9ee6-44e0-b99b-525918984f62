{"version": 3, "sources": [], "sections": [{"offset": {"line": 13, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Development/My%20Projects/Portfolio/my-app/src/components/ui/theme-provider.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { ThemeProvider as NextThemesProvider } from \"next-themes\"\nimport { type ThemeProviderProps } from \"next-themes/dist/types\"\n\nexport function ThemeProvider({ children, ...props }: ThemeProviderProps) {\n  return <NextThemesProvider {...props}>{children}</NextThemesProvider>\n}\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAMO,SAAS,cAAc,EAAE,QAAQ,EAAE,GAAG,OAA2B;IACtE,qBAAO,8OAAC,gJAAA,CAAA,gBAAkB;QAAE,GAAG,KAAK;kBAAG;;;;;;AACzC", "debugId": null}}, {"offset": {"line": 35, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Development/My%20Projects/Portfolio/my-app/src/components/ui/smoothCursor.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { motion, useSpring } from \"framer-motion\";\r\nimport { FC, JSX, useEffect, useRef, useState } from \"react\";\r\n\r\ninterface Position {\r\n  x: number;\r\n  y: number;\r\n}\r\n\r\nexport interface SmoothCursorProps {\r\n  cursor?: JSX.Element;\r\n  springConfig?: {\r\n    damping: number;\r\n    stiffness: number;\r\n    mass: number;\r\n    restDelta: number;\r\n  };\r\n}\r\n\r\nconst DefaultCursorSVG: FC = () => {\r\n  return (\r\n    <svg\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n      width={50}\r\n      height={54}\r\n      viewBox=\"0 0 50 54\"\r\n      fill=\"none\"\r\n      style={{ scale: 0.5 }}\r\n    >\r\n      <g filter=\"url(#filter0_d_91_7928)\">\r\n        <path\r\n          d=\"M42.6817 41.1495L27.5103 6.79925C26.7269 5.02557 24.2082 5.02558 23.3927 6.79925L7.59814 41.1495C6.75833 42.9759 8.52712 44.8902 10.4125 44.1954L24.3757 39.0496C24.8829 38.8627 25.4385 38.8627 25.9422 39.0496L39.8121 44.1954C41.6849 44.8902 43.4884 42.9759 42.6817 41.1495Z\"\r\n          fill=\"black\"\r\n        />\r\n        <path\r\n          d=\"M43.7146 40.6933L28.5431 6.34306C27.3556 3.65428 23.5772 3.69516 22.3668 6.32755L6.57226 40.6778C5.3134 43.4156 7.97238 46.298 10.803 45.2549L24.7662 40.109C25.0221 40.0147 25.2999 40.0156 25.5494 40.1082L39.4193 45.254C42.2261 46.2953 44.9254 43.4347 43.7146 40.6933Z\"\r\n          stroke=\"white\"\r\n          strokeWidth={2.25825}\r\n        />\r\n      </g>\r\n      <defs>\r\n        <filter\r\n          id=\"filter0_d_91_7928\"\r\n          x={0.602397}\r\n          y={0.952444}\r\n          width={49.0584}\r\n          height={52.428}\r\n          filterUnits=\"userSpaceOnUse\"\r\n          colorInterpolationFilters=\"sRGB\"\r\n        >\r\n          <feFlood floodOpacity={0} result=\"BackgroundImageFix\" />\r\n          <feColorMatrix\r\n            in=\"SourceAlpha\"\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0\"\r\n            result=\"hardAlpha\"\r\n          />\r\n          <feOffset dy={2.25825} />\r\n          <feGaussianBlur stdDeviation={2.25825} />\r\n          <feComposite in2=\"hardAlpha\" operator=\"out\" />\r\n          <feColorMatrix\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.08 0\"\r\n          />\r\n          <feBlend\r\n            mode=\"normal\"\r\n            in2=\"BackgroundImageFix\"\r\n            result=\"effect1_dropShadow_91_7928\"\r\n          />\r\n          <feBlend\r\n            mode=\"normal\"\r\n            in=\"SourceGraphic\"\r\n            in2=\"effect1_dropShadow_91_7928\"\r\n            result=\"shape\"\r\n          />\r\n        </filter>\r\n      </defs>\r\n    </svg>\r\n  );\r\n};\r\n\r\nexport function SmoothCursor({\r\n  cursor = <DefaultCursorSVG />,\r\n  springConfig = {\r\n    damping: 45,\r\n    stiffness: 400,\r\n    mass: 1,\r\n    restDelta: 0.001,\r\n  },\r\n}: SmoothCursorProps) {\r\n  const [isMoving, setIsMoving] = useState(false);\r\n  const lastMousePos = useRef<Position>({ x: 0, y: 0 });\r\n  const velocity = useRef<Position>({ x: 0, y: 0 });\r\n  const lastUpdateTime = useRef(Date.now());\r\n  const previousAngle = useRef(0);\r\n  const accumulatedRotation = useRef(0);\r\n\r\n  const cursorX = useSpring(0, springConfig);\r\n  const cursorY = useSpring(0, springConfig);\r\n  const rotation = useSpring(0, {\r\n    ...springConfig,\r\n    damping: 60,\r\n    stiffness: 300,\r\n  });\r\n  const scale = useSpring(1, {\r\n    ...springConfig,\r\n    stiffness: 500,\r\n    damping: 35,\r\n  });\r\n\r\n  useEffect(() => {\r\n    const updateVelocity = (currentPos: Position) => {\r\n      const currentTime = Date.now();\r\n      const deltaTime = currentTime - lastUpdateTime.current;\r\n\r\n      if (deltaTime > 0) {\r\n        velocity.current = {\r\n          x: (currentPos.x - lastMousePos.current.x) / deltaTime,\r\n          y: (currentPos.y - lastMousePos.current.y) / deltaTime,\r\n        };\r\n      }\r\n\r\n      lastUpdateTime.current = currentTime;\r\n      lastMousePos.current = currentPos;\r\n    };\r\n\r\n    const smoothMouseMove = (e: MouseEvent) => {\r\n      const currentPos = { x: e.clientX, y: e.clientY };\r\n      updateVelocity(currentPos);\r\n\r\n      const speed = Math.sqrt(\r\n        Math.pow(velocity.current.x, 2) + Math.pow(velocity.current.y, 2)\r\n      );\r\n\r\n      cursorX.set(currentPos.x);\r\n      cursorY.set(currentPos.y);\r\n\r\n      if (speed > 0.1) {\r\n        const currentAngle =\r\n          Math.atan2(velocity.current.y, velocity.current.x) * (180 / Math.PI) +\r\n          90;\r\n\r\n        let angleDiff = currentAngle - previousAngle.current;\r\n        if (angleDiff > 180) angleDiff -= 360;\r\n        if (angleDiff < -180) angleDiff += 360;\r\n        accumulatedRotation.current += angleDiff;\r\n        rotation.set(accumulatedRotation.current);\r\n        previousAngle.current = currentAngle;\r\n\r\n        scale.set(0.95);\r\n        setIsMoving(true);\r\n\r\n        const timeout = setTimeout(() => {\r\n          scale.set(1);\r\n          setIsMoving(false);\r\n        }, 150);\r\n\r\n        return () => clearTimeout(timeout);\r\n      }\r\n    };\r\n\r\n    let rafId: number;\r\n    const throttledMouseMove = (e: MouseEvent) => {\r\n      if (rafId) return;\r\n\r\n      rafId = requestAnimationFrame(() => {\r\n        smoothMouseMove(e);\r\n        rafId = 0;\r\n      });\r\n    };\r\n\r\n    document.body.style.cursor = \"none\";\r\n    window.addEventListener(\"mousemove\", throttledMouseMove);\r\n\r\n    return () => {\r\n      window.removeEventListener(\"mousemove\", throttledMouseMove);\r\n      document.body.style.cursor = \"auto\";\r\n      if (rafId) cancelAnimationFrame(rafId);\r\n    };\r\n  }, [cursorX, cursorY, rotation, scale]);\r\n\r\n  return (\r\n    <motion.div\r\n      style={{\r\n        position: \"fixed\",\r\n        left: cursorX,\r\n        top: cursorY,\r\n        translateX: \"-50%\",\r\n        translateY: \"-50%\",\r\n        rotate: rotation,\r\n        scale: scale,\r\n        zIndex: 100,\r\n        pointerEvents: \"none\",\r\n        willChange: \"transform\",\r\n      }}\r\n      initial={{ scale: 0 }}\r\n      animate={{ scale: 1 }}\r\n      transition={{\r\n        type: \"spring\",\r\n        stiffness: 400,\r\n        damping: 30,\r\n      }}\r\n    >\r\n      {cursor}\r\n    </motion.div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AACA;AAHA;;;;AAoBA,MAAM,mBAAuB;IAC3B,qBACE,8OAAC;QACC,OAAM;QACN,OAAO;QACP,QAAQ;QACR,SAAQ;QACR,MAAK;QACL,OAAO;YAAE,OAAO;QAAI;;0BAEpB,8OAAC;gBAAE,QAAO;;kCACR,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,QAAO;wBACP,aAAa;;;;;;;;;;;;0BAGjB,8OAAC;0BACC,cAAA,8OAAC;oBACC,IAAG;oBACH,GAAG;oBACH,GAAG;oBACH,OAAO;oBACP,QAAQ;oBACR,aAAY;oBACZ,2BAA0B;;sCAE1B,8OAAC;4BAAQ,cAAc;4BAAG,QAAO;;;;;;sCACjC,8OAAC;4BACC,IAAG;4BA<PERSON>,MAAK;4BACL,QAAO;4BACP,QAAO;;;;;;sCAET,8OAAC;4BAAS,IAAI;;;;;;sCACd,8OAAC;4BAAe,cAAc;;;;;;sCAC9B,8OAAC;4BAAY,KAAI;4BAAY,UAAS;;;;;;sCACtC,8OAAC;4BACC,MAAK;4BACL,QAAO;;;;;;sCAET,8OAAC;4BACC,MAAK;4BACL,KAAI;4BACJ,QAAO;;;;;;sCAET,8OAAC;4BACC,MAAK;4BACL,IAAG;4BACH,KAAI;4BACJ,QAAO;;;;;;;;;;;;;;;;;;;;;;;AAMnB;AAEO,SAAS,aAAa,EAC3B,uBAAS,8OAAC;;;;4CAAmB,EAC7B,eAAe;IACb,SAAS;IACT,WAAW;IACX,MAAM;IACN,WAAW;AACb,CAAC,EACiB;IAClB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAY;QAAE,GAAG;QAAG,GAAG;IAAE;IACnD,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAY;QAAE,GAAG;QAAG,GAAG;IAAE;IAC/C,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE,KAAK,GAAG;IACtC,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC7B,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAEnC,MAAM,UAAU,CAAA,GAAA,yKAAA,CAAA,YAAS,AAAD,EAAE,GAAG;IAC7B,MAAM,UAAU,CAAA,GAAA,yKAAA,CAAA,YAAS,AAAD,EAAE,GAAG;IAC7B,MAAM,WAAW,CAAA,GAAA,yKAAA,CAAA,YAAS,AAAD,EAAE,GAAG;QAC5B,GAAG,YAAY;QACf,SAAS;QACT,WAAW;IACb;IACA,MAAM,QAAQ,CAAA,GAAA,yKAAA,CAAA,YAAS,AAAD,EAAE,GAAG;QACzB,GAAG,YAAY;QACf,WAAW;QACX,SAAS;IACX;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,iBAAiB,CAAC;YACtB,MAAM,cAAc,KAAK,GAAG;YAC5B,MAAM,YAAY,cAAc,eAAe,OAAO;YAEtD,IAAI,YAAY,GAAG;gBACjB,SAAS,OAAO,GAAG;oBACjB,GAAG,CAAC,WAAW,CAAC,GAAG,aAAa,OAAO,CAAC,CAAC,IAAI;oBAC7C,GAAG,CAAC,WAAW,CAAC,GAAG,aAAa,OAAO,CAAC,CAAC,IAAI;gBAC/C;YACF;YAEA,eAAe,OAAO,GAAG;YACzB,aAAa,OAAO,GAAG;QACzB;QAEA,MAAM,kBAAkB,CAAC;YACvB,MAAM,aAAa;gBAAE,GAAG,EAAE,OAAO;gBAAE,GAAG,EAAE,OAAO;YAAC;YAChD,eAAe;YAEf,MAAM,QAAQ,KAAK,IAAI,CACrB,KAAK,GAAG,CAAC,SAAS,OAAO,CAAC,CAAC,EAAE,KAAK,KAAK,GAAG,CAAC,SAAS,OAAO,CAAC,CAAC,EAAE;YAGjE,QAAQ,GAAG,CAAC,WAAW,CAAC;YACxB,QAAQ,GAAG,CAAC,WAAW,CAAC;YAExB,IAAI,QAAQ,KAAK;gBACf,MAAM,eACJ,KAAK,KAAK,CAAC,SAAS,OAAO,CAAC,CAAC,EAAE,SAAS,OAAO,CAAC,CAAC,IAAI,CAAC,MAAM,KAAK,EAAE,IACnE;gBAEF,IAAI,YAAY,eAAe,cAAc,OAAO;gBACpD,IAAI,YAAY,KAAK,aAAa;gBAClC,IAAI,YAAY,CAAC,KAAK,aAAa;gBACnC,oBAAoB,OAAO,IAAI;gBAC/B,SAAS,GAAG,CAAC,oBAAoB,OAAO;gBACxC,cAAc,OAAO,GAAG;gBAExB,MAAM,GAAG,CAAC;gBACV,YAAY;gBAEZ,MAAM,UAAU,WAAW;oBACzB,MAAM,GAAG,CAAC;oBACV,YAAY;gBACd,GAAG;gBAEH,OAAO,IAAM,aAAa;YAC5B;QACF;QAEA,IAAI;QACJ,MAAM,qBAAqB,CAAC;YAC1B,IAAI,OAAO;YAEX,QAAQ,sBAAsB;gBAC5B,gBAAgB;gBAChB,QAAQ;YACV;QACF;QAEA,SAAS,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG;QAC7B,OAAO,gBAAgB,CAAC,aAAa;QAErC,OAAO;YACL,OAAO,mBAAmB,CAAC,aAAa;YACxC,SAAS,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG;YAC7B,IAAI,OAAO,qBAAqB;QAClC;IACF,GAAG;QAAC;QAAS;QAAS;QAAU;KAAM;IAEtC,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,OAAO;YACL,UAAU;YACV,MAAM;YACN,KAAK;YACL,YAAY;YACZ,YAAY;YACZ,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,eAAe;YACf,YAAY;QACd;QACA,SAAS;YAAE,OAAO;QAAE;QACpB,SAAS;YAAE,OAAO;QAAE;QACpB,YAAY;YACV,MAAM;YACN,WAAW;YACX,SAAS;QACX;kBAEC;;;;;;AAGP", "debugId": null}}]}