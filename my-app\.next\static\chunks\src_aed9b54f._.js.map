{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Development/My%20Projects/Portfolio/my-app/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Development/My%20Projects/Portfolio/my-app/src/components/ui/heroSectionBG.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport { motion } from \"framer-motion\"\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction ElegantShape({\r\n  className,\r\n  delay = 0,\r\n  width = 400,\r\n  height = 100,\r\n  rotate = 0,\r\n  gradient = \"from-white/[0.08]\",\r\n}: {\r\n  className?: string\r\n  delay?: number\r\n  width?: number\r\n  height?: number\r\n  rotate?: number\r\n  gradient?: string\r\n}) {\r\n  return (\r\n    <motion.div\r\n      initial={{\r\n        opacity: 0,\r\n        y: -150,\r\n        rotate: rotate - 15,\r\n      }}\r\n      animate={{\r\n        opacity: 1,\r\n        y: 0,\r\n        rotate: rotate,\r\n      }}\r\n      transition={{\r\n        duration: 2.4,\r\n        delay,\r\n        ease: [0.23, 0.86, 0.39, 0.96],\r\n        opacity: { duration: 1.2 },\r\n      }}\r\n      className={cn(\"absolute\", className)}\r\n    >\r\n      <motion.div\r\n        animate={{\r\n          y: [0, 15, 0],\r\n        }}\r\n        transition={{\r\n          duration: 12,\r\n          repeat: Number.POSITIVE_INFINITY,\r\n          ease: \"easeInOut\",\r\n        }}\r\n        style={{\r\n          width,\r\n          height,\r\n        }}\r\n        className=\"relative\"\r\n      >\r\n        <div\r\n          className={cn(\r\n            \"absolute inset-0 rounded-full\",\r\n            \"bg-gradient-to-r to-transparent\",\r\n            gradient,\r\n            \"backdrop-blur-[2px] border-2 border-white/[0.15] dark:border-white/[0.15] border-black/[0.15]\",\r\n            \"shadow-[0_8px_32px_0_rgba(0,0,0,0.1)] dark:shadow-[0_8px_32px_0_rgba(255,255,255,0.1)]\",\r\n            \"after:absolute after:inset-0 after:rounded-full\",\r\n            \"after:bg-[radial-gradient(circle_at_50%_50%,rgba(0,0,0,0.2),transparent_70%)] dark:after:bg-[radial-gradient(circle_at_50%_50%,rgba(255,255,255,0.2),transparent_70%)]\",\r\n          )}\r\n        />\r\n      </motion.div>\r\n    </motion.div>\r\n  )\r\n}\r\n\r\nexport default function HeroSectionBG() {\r\n  return (\r\n    <div className=\"absolute min-h-screen w-full flex items-center justify-center overflow-hidden bg-white dark:bg-[#030303]\">\r\n      <div className=\"absolute inset-0 bg-gradient-to-br from-indigo-500/[0.05] via-transparent to-rose-500/[0.05] blur-3xl\" />\r\n\r\n      <div className=\"absolute inset-0 overflow-hidden\">\r\n        <ElegantShape\r\n          delay={0.3}\r\n          width={600}\r\n          height={140}\r\n          rotate={12}\r\n          gradient=\"from-indigo-500/[0.15]\"\r\n          className=\"left-[-10%] md:left-[-5%] top-[15%] md:top-[20%]\"\r\n        />\r\n\r\n        <ElegantShape\r\n          delay={0.5}\r\n          width={500}\r\n          height={120}\r\n          rotate={-15}\r\n          gradient=\"from-rose-500/[0.15]\"\r\n          className=\"right-[-5%] md:right-[0%] top-[70%] md:top-[75%]\"\r\n        />\r\n\r\n        <ElegantShape\r\n          delay={0.4}\r\n          width={300}\r\n          height={80}\r\n          rotate={-8}\r\n          gradient=\"from-violet-500/[0.15]\"\r\n          className=\"left-[5%] md:left-[10%] bottom-[5%] md:bottom-[10%]\"\r\n        />\r\n\r\n        <ElegantShape\r\n          delay={0.6}\r\n          width={200}\r\n          height={60}\r\n          rotate={20}\r\n          gradient=\"from-amber-500/[0.15]\"\r\n          className=\"right-[15%] md:right-[20%] top-[10%] md:top-[15%]\"\r\n        />\r\n\r\n        <ElegantShape\r\n          delay={0.7}\r\n          width={150}\r\n          height={40}\r\n          rotate={-25}\r\n          gradient=\"from-cyan-500/[0.15]\"\r\n          className=\"left-[20%] md:left-[25%] top-[5%] md:top-[10%]\"\r\n        />\r\n      </div>\r\n\r\n      <div className=\"absolute inset-0 bg-gradient-to-t from-white via-transparent to-white/80 dark:from-[#030303] dark:via-transparent dark:to-[#030303]/80 pointer-events-none\" />\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKA,SAAS,aAAa,KAcrB;QAdqB,EACpB,SAAS,EACT,QAAQ,CAAC,EACT,QAAQ,GAAG,EACX,SAAS,GAAG,EACZ,SAAS,CAAC,EACV,WAAW,mBAAmB,EAQ/B,GAdqB;IAepB,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YACP,SAAS;YACT,GAAG,CAAC;YACJ,QAAQ,SAAS;QACnB;QACA,SAAS;YACP,SAAS;YACT,GAAG;YACH,QAAQ;QACV;QACA,YAAY;YACV,UAAU;YACV;YACA,MAAM;gBAAC;gBAAM;gBAAM;gBAAM;aAAK;YAC9B,SAAS;gBAAE,UAAU;YAAI;QAC3B;QACA,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;kBAE1B,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;YACT,SAAS;gBACP,GAAG;oBAAC;oBAAG;oBAAI;iBAAE;YACf;YACA,YAAY;gBACV,UAAU;gBACV,QAAQ,OAAO,iBAAiB;gBAChC,MAAM;YACR;YACA,OAAO;gBACL;gBACA;YACF;YACA,WAAU;sBAEV,cAAA,6LAAC;gBACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iCACA,mCACA,UACA,iGACA,0FACA,mDACA;;;;;;;;;;;;;;;;AAMZ;KAhES;AAkEM,SAAS;IACtB,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;;;;;0BAEf,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,OAAO;wBACP,OAAO;wBACP,QAAQ;wBACR,QAAQ;wBACR,UAAS;wBACT,WAAU;;;;;;kCAGZ,6LAAC;wBACC,OAAO;wBACP,OAAO;wBACP,QAAQ;wBACR,QAAQ,CAAC;wBACT,UAAS;wBACT,WAAU;;;;;;kCAGZ,6LAAC;wBACC,OAAO;wBACP,OAAO;wBACP,QAAQ;wBACR,QAAQ,CAAC;wBACT,UAAS;wBACT,WAAU;;;;;;kCAGZ,6LAAC;wBACC,OAAO;wBACP,OAAO;wBACP,QAAQ;wBACR,QAAQ;wBACR,UAAS;wBACT,WAAU;;;;;;kCAGZ,6LAAC;wBACC,OAAO;wBACP,OAAO;wBACP,QAAQ;wBACR,QAAQ,CAAC;wBACT,UAAS;wBACT,WAAU;;;;;;;;;;;;0BAId,6LAAC;gBAAI,WAAU;;;;;;;;;;;;AAGrB;MAvDwB", "debugId": null}}, {"offset": {"line": 210, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Development/My%20Projects/Portfolio/my-app/src/components/ui/homePage.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\";\r\nimport Image from \"next/image\";\r\nimport FlipLink from \"@/components/ui/text-effect-flipper\";\r\nimport dynamic from \"next/dynamic\";\r\n\r\nconst HomePage = () => {\r\n  return (\r\n    <div className=\"relative min-h-screen overflow-hidden px-4 sm:px-6 lg:px-8\">\r\n      {/* Main heading with responsive text sizes and layout */}\r\n      <h1\r\n        className=\"text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl\r\n                     flex flex-col sm:flex-row items-center justify-center\r\n                     mt-20 sm:mt-24 md:mt-32 lg:mt-40 xl:mt-100\r\n                     font-bold text-center gap-2 sm:gap-0\"\r\n      >\r\n        <span className=\"mb-2 sm:mb-0 sm:mr-2\">I&apos;m</span>\r\n        <Image\r\n          src=\"/profilePhoto.jpg\"\r\n          alt=\"Profile photo of Parth Chauhan\"\r\n          width={80}\r\n          height={80}\r\n          className=\"w-18 h-12 sm:w-16 sm:h-16 md:w-18 md:h-18 lg:w-30 lg:h-20\r\n                     rounded-xl mx-2 object-cover flex-shrink-0\"\r\n        />\r\n        <span className=\"mt-2 sm:mt-0 sm:ml-2\">Parth Chauhan</span>\r\n      </h1>\r\n\r\n      {/* Secondary heading with responsive text and layout */}\r\n      <h2\r\n        className=\"text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl 2xl:text-7xl\r\n                     flex flex-col sm:flex-row items-center justify-center\r\n                     mt-4 sm:mt-6 md:mt-8 lg:mt-10\r\n                     font-bold font-[skiper] text-center gap-2 sm:gap-1\r\n                     px-2 sm:px-4\"\r\n      >\r\n        <span className=\"mb-2 sm:mb-0\">Bringing Ideas to</span>\r\n        <span\r\n          className=\"text-black bg-yellow-300 px-2 py-1 sm:px-3 sm:py-2\r\n                         rounded-lg text-xl sm:text-2xl md:text-3xl lg:text-4xl xl:text-5xl 2xl:text-6xl\r\n                         whitespace-nowrap\"\r\n        >\r\n          Reality\r\n        </span>\r\n      </h2>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default HomePage;\r\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAOA,MAAM,WAAW;IACf,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBACC,WAAU;;kCAKV,6LAAC;wBAAK,WAAU;kCAAuB;;;;;;kCACvC,6LAAC,gIAAA,CAAA,UAAK;wBACJ,KAAI;wBACJ,KAAI;wBACJ,OAAO;wBACP,QAAQ;wBACR,WAAU;;;;;;kCAGZ,6LAAC;wBAAK,WAAU;kCAAuB;;;;;;;;;;;;0BAIzC,6LAAC;gBACC,WAAU;;kCAMV,6LAAC;wBAAK,WAAU;kCAAe;;;;;;kCAC/B,6LAAC;wBACC,WAAU;kCAGX;;;;;;;;;;;;;;;;;;AAMT;KAzCM;uCA2CS", "debugId": null}}, {"offset": {"line": 303, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Development/My%20Projects/Portfolio/my-app/src/components/ui/text-effect-flipper.tsx"], "sourcesContent": ["\"use client\";\n\nimport React from \"react\";\nimport { motion } from \"framer-motion\";\n\nconst DURATION = 0.30;\nconst STAGGER = 0.025;\n\ninterface FlipLinkProps {\n  children: string;\n  href: string;\n}\n\nconst FlipLink: React.FC<FlipLinkProps> = ({ children, href }) => {\n  return (\n    <motion.a\n      initial=\"initial\"\n      whileHover=\"hovered\"\n      target=\"_blank\"\n      href={href}\n      className=\"relative block overflow-hidden whitespace-nowrap text-4xl font-semibold uppercase dark:text-white/90 sm:text-7xl md:text-8xl \"\n      style={{\n        lineHeight: 0.75,\n      }}\n    >\n      <div>\n        {children.split(\"\").map((l, i) => (\n          <motion.span\n            variants={{\n              initial: {\n                y: 0,\n              },\n              hovered: {\n                y: \"-100%\",\n              },\n            }}\n            transition={{\n              duration: DURATION,\n              ease: \"easeInOut\",\n              delay: STAGGER * i,\n            }}\n            className=\"inline-block\"\n            key={i}\n          >\n            {l}\n          </motion.span>\n        ))}\n      </div>\n      <div className=\"absolute inset-0\">\n        {children.split(\"\").map((l, i) => (\n          <motion.span\n            variants={{\n              initial: {\n                y: \"100%\",\n              },\n              hovered: {\n                y: 0,\n              },\n            }}\n            transition={{\n              duration: DURATION,\n              ease: \"easeInOut\",\n              delay: STAGGER * i,\n            }}\n            className=\"inline-block\"\n            key={i}\n          >\n            {l}\n          </motion.span>\n        ))}\n      </div>\n    </motion.a>\n  );\n};\n\nexport default FlipLink;\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAKA,MAAM,WAAW;AACjB,MAAM,UAAU;AAOhB,MAAM,WAAoC;QAAC,EAAE,QAAQ,EAAE,IAAI,EAAE;IAC3D,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;QACP,SAAQ;QACR,YAAW;QACX,QAAO;QACP,MAAM;QACN,WAAU;QACV,OAAO;YACL,YAAY;QACd;;0BAEA,6LAAC;0BACE,SAAS,KAAK,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,kBAC1B,6LAAC,6LAAA,CAAA,SAAM,CAAC,IAAI;wBACV,UAAU;4BACR,SAAS;gCACP,GAAG;4BACL;4BACA,SAAS;gCACP,GAAG;4BACL;wBACF;wBACA,YAAY;4BACV,UAAU;4BACV,MAAM;4BACN,OAAO,UAAU;wBACnB;wBACA,WAAU;kCAGT;uBAFI;;;;;;;;;;0BAMX,6LAAC;gBAAI,WAAU;0BACZ,SAAS,KAAK,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,kBAC1B,6LAAC,6LAAA,CAAA,SAAM,CAAC,IAAI;wBACV,UAAU;4BACR,SAAS;gCACP,GAAG;4BACL;4BACA,SAAS;gCACP,GAAG;4BACL;wBACF;wBACA,YAAY;4BACV,UAAU;4BACV,MAAM;4BACN,OAAO,UAAU;wBACnB;wBACA,WAAU;kCAGT;uBAFI;;;;;;;;;;;;;;;;AAQjB;KA5DM;uCA8DS", "debugId": null}}, {"offset": {"line": 400, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Development/My%20Projects/Portfolio/my-app/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,KASb;QATa,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF,GATa;IAUd,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 464, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Development/My%20Projects/Portfolio/my-app/src/components/ui/theme-animations.ts"], "sourcesContent": ["export type AnimationVariant = \"circle\" | \"circle-blur\" | \"polygon\" | \"gif\"\nexport type AnimationStart =\n  | \"top-left\"\n  | \"top-right\"\n  | \"bottom-left\"\n  | \"bottom-right\"\n  | \"center\"\n\ninterface Animation {\n  name: string\n  css: string\n}\n\nconst getPositionCoords = (position: AnimationStart) => {\n  switch (position) {\n    case \"top-left\":\n      return { cx: \"0\", cy: \"0\" }\n    case \"top-right\":\n      return { cx: \"40\", cy: \"0\" }\n    case \"bottom-left\":\n      return { cx: \"0\", cy: \"40\" }\n    case \"bottom-right\":\n      return { cx: \"40\", cy: \"40\" }\n  }\n}\n\nconst generateSVG = (variant: AnimationVariant, start: AnimationStart) => {\n  if (start === \"center\") return\n\n  const positionCoords = getPositionCoords(start)\n  if (!positionCoords) {\n    throw new Error(`Invalid start position: ${start}`)\n  }\n  const { cx, cy } = positionCoords\n\n  if (variant === \"circle\") {\n    return `data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 40 40\"><circle cx=\"${cx}\" cy=\"${cy}\" r=\"20\" fill=\"white\"/></svg>`\n  }\n\n  if (variant === \"circle-blur\") {\n    return `data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 40 40\"><defs><filter id=\"blur\"><feGaussianBlur stdDeviation=\"2\"/></filter></defs><circle cx=\"${cx}\" cy=\"${cy}\" r=\"18\" fill=\"white\" filter=\"url(%23blur)\"/></svg>`\n  }\n\n  return \"\"\n}\n\nconst getTransformOrigin = (start: AnimationStart) => {\n  switch (start) {\n    case \"top-left\":\n      return \"top left\"\n    case \"top-right\":\n      return \"top right\"\n    case \"bottom-left\":\n      return \"bottom left\"\n    case \"bottom-right\":\n      return \"bottom right\"\n  }\n}\n\nexport const createAnimation = (\n  variant: AnimationVariant,\n  start: AnimationStart,\n  url?: string\n): Animation => {\n  const svg = generateSVG(variant, start)\n  const transformOrigin = getTransformOrigin(start)\n\n  if (variant === \"polygon\") {\n    return {\n      name: `${variant}-${start}`,\n      css: `\n       ::view-transition-group(root) {\n        animation-duration: 0.7s;\n        animation-timing-function: var(--expo-out);\n      }\n            \n      ::view-transition-new(root) {\n        animation-name: reveal-light;\n      }\n\n      ::view-transition-old(root),\n      .dark::view-transition-old(root) {\n        animation: none;\n        z-index: -1;\n      }\n      .dark::view-transition-new(root) {\n        animation-name: reveal-dark;\n      }\n\n      @keyframes reveal-dark {\n        from {\n          clip-path: polygon(50% -71%, -50% 71%, -50% 71%, 50% -71%);\n        }\n        to {\n          clip-path: polygon(50% -71%, -50% 71%, 50% 171%, 171% 50%);\n        }\n      }\n\n      @keyframes reveal-light {\n        from {\n          clip-path: polygon(171% 50%, 50% 171%, 50% 171%, 171% 50%);\n        }\n        to {\n          clip-path: polygon(171% 50%, 50% 171%, -50% 71%, 50% -71%);\n        }\n      }\n      `,\n    }\n  }\n  if (variant === \"circle\" && start == \"center\") {\n    return {\n      name: `${variant}-${start}`,\n      css: `\n       ::view-transition-group(root) {\n        animation-duration: 0.7s;\n        animation-timing-function: var(--expo-out);\n      }\n            \n      ::view-transition-new(root) {\n        animation-name: reveal-light;\n      }\n\n      ::view-transition-old(root),\n      .dark::view-transition-old(root) {\n        animation: none;\n        z-index: -1;\n      }\n      .dark::view-transition-new(root) {\n        animation-name: reveal-dark;\n      }\n\n      @keyframes reveal-dark {\n        from {\n          clip-path: circle(0% at 50% 50%);\n        }\n        to {\n          clip-path: circle(100.0% at 50% 50%);\n        }\n      }\n\n      @keyframes reveal-light {\n        from {\n           clip-path: circle(0% at 50% 50%);\n        }\n        to {\n          clip-path: circle(100.0% at 50% 50%);\n        }\n      }\n      `,\n    }\n  }\n  if (variant === \"gif\") {\n    return {\n      name: `${variant}-${start}`,\n      css: `\n      ::view-transition-group(root) {\n  animation-timing-function: var(--expo-in);\n}\n\n::view-transition-new(root) {\n  mask: url('${url}') center / 0 no-repeat;\n  animation: scale 3s;\n}\n\n::view-transition-old(root),\n.dark::view-transition-old(root) {\n  animation: scale 3s;\n}\n\n@keyframes scale {\n  0% {\n    mask-size: 0;\n  }\n  10% {\n    mask-size: 50vmax;\n  }\n  90% {\n    mask-size: 50vmax;\n  }\n  100% {\n    mask-size: 2000vmax;\n  }\n}`,\n    }\n  }\n\n  return {\n    name: `${variant}-${start}`,\n    css: `\n      ::view-transition-group(root) {\n        animation-timing-function: var(--expo-out);\n      }\n      ::view-transition-new(root) {\n        mask: url('${svg}') ${start.replace(\"-\", \" \")} / 0 no-repeat;\n        mask-origin: content-box;\n        animation: scale-${start} 1s;\n        transform-origin: ${transformOrigin};\n      }\n      ::view-transition-old(root),\n      .dark::view-transition-old(root) {\n        animation: scale-${start} 1s;\n        transform-origin: ${transformOrigin};\n        z-index: -1;\n      }\n      @keyframes scale-${start} {\n        to {\n          mask-size: 350vmax;\n        }\n      }\n    `,\n  }\n}\n"], "names": [], "mappings": ";;;AAaA,MAAM,oBAAoB,CAAC;IACzB,OAAQ;QACN,KAAK;YACH,OAAO;gBAAE,IAAI;gBAAK,IAAI;YAAI;QAC5B,KAAK;YACH,OAAO;gBAAE,IAAI;gBAAM,IAAI;YAAI;QAC7B,KAAK;YACH,OAAO;gBAAE,IAAI;gBAAK,IAAI;YAAK;QAC7B,KAAK;YACH,OAAO;gBAAE,IAAI;gBAAM,IAAI;YAAK;IAChC;AACF;AAEA,MAAM,cAAc,CAAC,SAA2B;IAC9C,IAAI,UAAU,UAAU;IAExB,MAAM,iBAAiB,kBAAkB;IACzC,IAAI,CAAC,gBAAgB;QACnB,MAAM,IAAI,MAAM,AAAC,2BAAgC,OAAN;IAC7C;IACA,MAAM,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG;IAEnB,IAAI,YAAY,UAAU;QACxB,OAAO,AAAC,8FAAwG,OAAX,IAAG,UAAW,OAAH,IAAG;IACrH;IAEA,IAAI,YAAY,eAAe;QAC7B,OAAO,AAAC,wKAAkL,OAAX,IAAG,UAAW,OAAH,IAAG;IAC/L;IAEA,OAAO;AACT;AAEA,MAAM,qBAAqB,CAAC;IAC1B,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;IACX;AACF;AAEO,MAAM,kBAAkB,CAC7B,SACA,OACA;IAEA,MAAM,MAAM,YAAY,SAAS;IACjC,MAAM,kBAAkB,mBAAmB;IAE3C,IAAI,YAAY,WAAW;QACzB,OAAO;YACL,MAAM,AAAC,GAAa,OAAX,SAAQ,KAAS,OAAN;YACpB,KAAM;QAqCR;IACF;IACA,IAAI,YAAY,YAAY,SAAS,UAAU;QAC7C,OAAO;YACL,MAAM,AAAC,GAAa,OAAX,SAAQ,KAAS,OAAN;YACpB,KAAM;QAqCR;IACF;IACA,IAAI,YAAY,OAAO;QACrB,OAAO;YACL,MAAM,AAAC,GAAa,OAAX,SAAQ,KAAS,OAAN;YACpB,KAAK,AAAC,2IAMO,OAAJ,KAAI;QAuBf;IACF;IAEA,OAAO;QACL,MAAM,AAAC,GAAa,OAAX,SAAQ,KAAS,OAAN;QACpB,KAAK,AAAC,kKAKoB,OAAT,KAAI,OAEE,OAFG,MAAM,OAAO,CAAC,KAAK,MAAK,iFAG1B,OADD,OAAM,oCAKN,OAJC,iBAAgB,uHAKhB,OADD,OAAM,oCAIR,OAHG,iBAAgB,6DAGb,OAAN,OAAM;IAM7B;AACF", "debugId": null}}, {"offset": {"line": 553, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Development/My%20Projects/Portfolio/my-app/src/components/ui/theme-toggle-button.tsx"], "sourcesContent": ["\"use client\";\n\nimport React from \"react\";\nimport { MoonIcon, SunIcon } from \"lucide-react\";\nimport { useTheme } from \"next-themes\";\n\nimport { Button } from \"@/components/ui/button\";\n\nimport {\n  AnimationStart,\n  AnimationVariant,\n  createAnimation,\n} from \"./theme-animations\";\n\ninterface ThemeToggleAnimationProps {\n  variant?: AnimationVariant;\n  start?: AnimationStart;\n  showLabel?: boolean;\n  url?: string;\n}\n\nexport default function ThemeToggleButton({\n  variant = \"circle-blur\",\n  start = \"top-left\",\n  showLabel = false,\n  url = \"\",\n}: ThemeToggleAnimationProps) {\n  const { theme, setTheme } = useTheme();\n\n  const styleId = \"theme-transition-styles\";\n\n  const updateStyles = React.useCallback((css: string, name: string) => {\n    if (typeof window === \"undefined\") return;\n\n    let styleElement = document.getElementById(styleId) as HTMLStyleElement;\n\n    console.log(\"style ELement\", styleElement);\n    console.log(\"name\", name);\n\n    if (!styleElement) {\n      styleElement = document.createElement(\"style\");\n      styleElement.id = styleId;\n      document.head.appendChild(styleElement);\n    }\n\n    styleElement.textContent = css;\n\n    console.log(\"content updated\");\n  }, []);\n\n  const toggleTheme = React.useCallback(() => {\n    const animation = createAnimation(variant, start, url);\n\n    updateStyles(animation.css, animation.name);\n\n    if (typeof window === \"undefined\") return;\n\n    const switchTheme = () => {\n      setTheme(theme === \"light\" ? \"dark\" : \"light\");\n    };\n\n    if (!document.startViewTransition) {\n      switchTheme();\n      return;\n    }\n\n    document.startViewTransition(switchTheme);\n  }, [theme, setTheme]);\n\n  return (\n    <Button\n      onClick={toggleTheme}\n      variant=\"ghost\"\n      size=\"icon\"\n      className=\"w-8 h-8 sm:w-9 sm:h-9 p-0 relative group\"\n      name=\"Theme Toggle Button\"\n    >\n      <SunIcon className=\"size-4 sm:size-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0\" />\n      <MoonIcon className=\"absolute size-4 sm:size-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100\" />\n      <span className=\"sr-only\">Theme Toggle </span>\n      {showLabel && (\n        <>\n          <span className=\"hidden group-hover:block border rounded-full px-2 absolute -top-10\">\n            {\" \"}\n            variant = {variant}\n          </span>\n          <span className=\"hidden group-hover:block border rounded-full px-2 absolute -bottom-10\">\n            {\" \"}\n            start = {start}\n          </span>\n        </>\n      )}\n    </Button>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAEA;AAEA;;;AARA;;;;;;AAqBe,SAAS,kBAAkB,KAKd;QALc,EACxC,UAAU,aAAa,EACvB,QAAQ,UAAU,EAClB,YAAY,KAAK,EACjB,MAAM,EAAE,EACkB,GALc;;IAMxC,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,WAAQ,AAAD;IAEnC,MAAM,UAAU;IAEhB,MAAM,eAAe,6JAAA,CAAA,UAAK,CAAC,WAAW;uDAAC,CAAC,KAAa;YACnD;;YAEA,IAAI,eAAe,SAAS,cAAc,CAAC;YAE3C,QAAQ,GAAG,CAAC,iBAAiB;YAC7B,QAAQ,GAAG,CAAC,QAAQ;YAEpB,IAAI,CAAC,cAAc;gBACjB,eAAe,SAAS,aAAa,CAAC;gBACtC,aAAa,EAAE,GAAG;gBAClB,SAAS,IAAI,CAAC,WAAW,CAAC;YAC5B;YAEA,aAAa,WAAW,GAAG;YAE3B,QAAQ,GAAG,CAAC;QACd;sDAAG,EAAE;IAEL,MAAM,cAAc,6JAAA,CAAA,UAAK,CAAC,WAAW;sDAAC;YACpC,MAAM,YAAY,CAAA,GAAA,iJAAA,CAAA,kBAAe,AAAD,EAAE,SAAS,OAAO;YAElD,aAAa,UAAU,GAAG,EAAE,UAAU,IAAI;YAE1C;;YAEA,MAAM;0EAAc;oBAClB,SAAS,UAAU,UAAU,SAAS;gBACxC;;YAEA,IAAI,CAAC,SAAS,mBAAmB,EAAE;gBACjC;gBACA;YACF;YAEA,SAAS,mBAAmB,CAAC;QAC/B;qDAAG;QAAC;QAAO;KAAS;IAEpB,qBACE,6LAAC,qIAAA,CAAA,SAAM;QACL,SAAS;QACT,SAAQ;QACR,MAAK;QACL,WAAU;QACV,MAAK;;0BAEL,6LAAC,uMAAA,CAAA,UAAO;gBAAC,WAAU;;;;;;0BACnB,6LAAC,yMAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;0BACpB,6LAAC;gBAAK,WAAU;0BAAU;;;;;;YACzB,2BACC;;kCACE,6LAAC;wBAAK,WAAU;;4BACb;4BAAI;4BACM;;;;;;;kCAEb,6LAAC;wBAAK,WAAU;;4BACb;4BAAI;4BACI;;;;;;;;;;;;;;;AAMrB;GAzEwB;;QAMM,mJAAA,CAAA,WAAQ;;;KANd", "debugId": null}}, {"offset": {"line": 694, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Development/My%20Projects/Portfolio/my-app/src/components/ui/contactMeButton.tsx"], "sourcesContent": ["\"use client\";\r\nimport React, { useState, useEffect } from \"react\";\r\n\r\ntype FormState = \"idle\" | \"loading\" | \"success\";\r\n\r\nexport default function ContactMeButton() {\r\n  const [formState, setFormState] = useState<FormState>(\"idle\");\r\n  const [open, setOpen] = useState(false);\r\n  const [feedback, setFeedback] = useState(\"\");\r\n\r\n  function submit() {\r\n    setFormState(\"loading\");\r\n    setTimeout(() => {\r\n      setFormState(\"success\");\r\n    }, 1500);\r\n\r\n    setTimeout(() => {\r\n      setOpen(false);\r\n      setFormState(\"idle\");\r\n      setFeedback(\"\");\r\n    }, 3300);\r\n  }\r\n\r\n  useEffect(() => {\r\n    const handleKeyDown = (event: KeyboardEvent) => {\r\n      if (event.key === \"Escape\") setOpen(false);\r\n\r\n      if (\r\n        (event.ctrlKey || event.metaKey) &&\r\n        event.key === \"Enter\" &&\r\n        open &&\r\n        formState === \"idle\"\r\n      ) {\r\n        submit();\r\n      }\r\n    };\r\n\r\n    window.addEventListener(\"keydown\", handleKeyDown);\r\n    return () => window.removeEventListener(\"keydown\", handleKeyDown);\r\n  }, [open, formState]);\r\n\r\n  return (\r\n    <>\r\n      {/* Contact Me Button */}\r\n      <button\r\n        onClick={() => setOpen(true)}\r\n        className=\"px-2 sm:px-3 md:px-4 py-1 sm:py-2 text-sm sm:text-base md:text-lg\r\n                    text-black bg-yellow-400\r\n                   rounded-full transition-all duration-300 hover:bg-yellow-500\r\n                   dark:text-gray-900 dark:bg-yellow-300 dark:hover:bg-yellow-400\r\n                   transform hover:scale-105 hover:shadow-md active:scale-95\r\n                   whitespace-nowrap font-[getInTouch]\"\r\n      >\r\n        Get in Touch\r\n      </button>\r\n\r\n      {/* Popup */}\r\n      {open && (\r\n        <div\r\n          className=\"fixed inset-0 z-50 flex items-center justify-center p-2 sm:p-4\r\n                        bg-black/30 backdrop-blur-md\"\r\n        >\r\n          {/* Overlay */}\r\n          <div className=\"absolute inset-0\" onClick={() => setOpen(false)} />\r\n\r\n          {/* Modal */}\r\n          <div\r\n            className=\"relative bg-white dark:bg-zinc-900 rounded-xl sm:rounded-2xl shadow-2xl\r\n                       w-full max-w-sm sm:max-w-md mx-2 sm:mx-4\r\n                       animate-in zoom-in-95 slide-in-from-bottom-4 duration-300\r\n                       border border-gray-200 dark:border-black\"\r\n          >\r\n            {/* Header */}\r\n            <div className=\"flex items-center justify-between p-4 sm:p-6 border-b border-gray-200 dark:border-gray-700\">\r\n              <div>\r\n                <h2 className=\"text-xl font-bold text-gray-900 dark:text-white\">\r\n                  Get In Touch\r\n                </h2>\r\n                <p className=\"text-sm text-gray-600 dark:text-gray-400 mt-1\">\r\n                  I'd love to hear from you! Send me a message.\r\n                </p>\r\n              </div>\r\n              <button\r\n                onClick={() => setOpen(false)}\r\n                className=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 \r\n                           w-8 h-8 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800\"\r\n              >\r\n                ✕\r\n              </button>\r\n            </div>\r\n\r\n            {formState === \"success\" ? (\r\n              <div className=\"p-6 text-center\">\r\n                <div className=\"w-16 h-16 bg-green-100 dark:bg-green-800 rounded-full flex items-center justify-center mx-auto mb-4\">\r\n                  <span className=\"text-2xl\">✅</span>\r\n                </div>\r\n                <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-2\">\r\n                  Message Sent Successfully!\r\n                </h3>\r\n                <p className=\"text-gray-600 dark:text-gray-400\">\r\n                  Thank you for reaching out! I'll get back to you within 24\r\n                  hours.\r\n                </p>\r\n              </div>\r\n            ) : (\r\n              <form\r\n                onSubmit={(e) => {\r\n                  e.preventDefault();\r\n                  if (!feedback.trim()) return;\r\n                  submit();\r\n                }}\r\n                className=\"p-4 sm:p-6 space-y-3 sm:space-y-4\"\r\n              >\r\n                <div>\r\n                  <label className=\"block text-sm font-semibold text-gray-800 dark:text-gray-200 mb-2\">\r\n                    Your Message\r\n                  </label>\r\n                  <textarea\r\n                    autoFocus\r\n                    placeholder=\"Hi Parth! I'd like to discuss...\"\r\n                    value={feedback}\r\n                    onChange={(e) => setFeedback(e.target.value)}\r\n                    className=\"w-full h-32 px-3 py-2 border border-gray-300 dark:border-gray-700\r\n                               rounded-lg focus:ring-2 focus:ring-yellow-400 focus:border-transparent\r\n                               bg-white dark:bg-zinc-800 text-gray-900 dark:text-white\r\n                               placeholder-gray-500 dark:placeholder-gray-400 transition-all\"\r\n                    required\r\n                    minLength={10}\r\n                  />\r\n                  <p className=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">\r\n                    Minimum 10 characters required\r\n                  </p>\r\n                </div>\r\n\r\n                <div className=\"flex flex-col sm:flex-row gap-2 sm:gap-3 pt-2\">\r\n                  <button\r\n                    type=\"button\"\r\n                    onClick={() => setOpen(false)}\r\n                    className=\"flex-1 px-3 sm:px-4 py-2 text-xs sm:text-sm font-semibold text-gray-700 dark:text-gray-300\r\n                               bg-gray-100 dark:bg-gray-800 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-700\r\n                               transition-colors duration-200\"\r\n                  >\r\n                    Cancel\r\n                  </button>\r\n                  <button\r\n                    type=\"submit\"\r\n                    disabled={\r\n                      formState === \"loading\" || feedback.trim().length < 10\r\n                    }\r\n                    className=\"flex-1 px-3 sm:px-4 py-2 text-xs sm:text-sm font-semibold text-black dark:text-gray-900\r\n                               bg-yellow-400 dark:bg-yellow-300 rounded-lg hover:bg-yellow-500 dark:hover:bg-yellow-400\r\n                               disabled:opacity-50 disabled:cursor-not-allowed\r\n                               transition-all duration-200 transform hover:scale-105 active:scale-95\r\n                               flex items-center justify-center gap-1 sm:gap-2\"\r\n                  >\r\n                    {formState === \"loading\" ? (\r\n                      <>\r\n                        <div className=\"w-3 h-3 sm:w-4 sm:h-4 border-2 border-black dark:border-gray-900 border-t-transparent rounded-full animate-spin\" />\r\n                        <span className=\"hidden sm:inline\">Sending...</span>\r\n                        <span className=\"sm:hidden\">...</span>\r\n                      </>\r\n                    ) : (\r\n                      <>\r\n                        <span className=\"hidden sm:inline\">Send Message</span>\r\n                        <span className=\"sm:hidden\">Send</span>\r\n                      </>\r\n                    )}\r\n                  </button>\r\n                </div>\r\n              </form>\r\n            )}\r\n          </div>\r\n        </div>\r\n      )}\r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AACA;;;AADA;;AAKe,SAAS;;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa;IACtD,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,SAAS;QACP,aAAa;QACb,WAAW;YACT,aAAa;QACf,GAAG;QAEH,WAAW;YACT,QAAQ;YACR,aAAa;YACb,YAAY;QACd,GAAG;IACL;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,MAAM;2DAAgB,CAAC;oBACrB,IAAI,MAAM,GAAG,KAAK,UAAU,QAAQ;oBAEpC,IACE,CAAC,MAAM,OAAO,IAAI,MAAM,OAAO,KAC/B,MAAM,GAAG,KAAK,WACd,QACA,cAAc,QACd;wBACA;oBACF;gBACF;;YAEA,OAAO,gBAAgB,CAAC,WAAW;YACnC;6CAAO,IAAM,OAAO,mBAAmB,CAAC,WAAW;;QACrD;oCAAG;QAAC;QAAM;KAAU;IAEpB,qBACE;;0BAEE,6LAAC;gBACC,SAAS,IAAM,QAAQ;gBACvB,WAAU;0BAMX;;;;;;YAKA,sBACC,6LAAC;gBACC,WAAU;;kCAIV,6LAAC;wBAAI,WAAU;wBAAmB,SAAS,IAAM,QAAQ;;;;;;kCAGzD,6LAAC;wBACC,WAAU;;0CAMV,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAkD;;;;;;0DAGhE,6LAAC;gDAAE,WAAU;0DAAgD;;;;;;;;;;;;kDAI/D,6LAAC;wCACC,SAAS,IAAM,QAAQ;wCACvB,WAAU;kDAEX;;;;;;;;;;;;4BAKF,cAAc,0BACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAK,WAAU;sDAAW;;;;;;;;;;;kDAE7B,6LAAC;wCAAG,WAAU;kDAA2D;;;;;;kDAGzE,6LAAC;wCAAE,WAAU;kDAAmC;;;;;;;;;;;qDAMlD,6LAAC;gCACC,UAAU,CAAC;oCACT,EAAE,cAAc;oCAChB,IAAI,CAAC,SAAS,IAAI,IAAI;oCACtB;gCACF;gCACA,WAAU;;kDAEV,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAAoE;;;;;;0DAGrF,6LAAC;gDACC,SAAS;gDACT,aAAY;gDACZ,OAAO;gDACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;gDAC3C,WAAU;gDAIV,QAAQ;gDACR,WAAW;;;;;;0DAEb,6LAAC;gDAAE,WAAU;0DAAgD;;;;;;;;;;;;kDAK/D,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,MAAK;gDACL,SAAS,IAAM,QAAQ;gDACvB,WAAU;0DAGX;;;;;;0DAGD,6LAAC;gDACC,MAAK;gDACL,UACE,cAAc,aAAa,SAAS,IAAI,GAAG,MAAM,GAAG;gDAEtD,WAAU;0DAMT,cAAc,0BACb;;sEACE,6LAAC;4DAAI,WAAU;;;;;;sEACf,6LAAC;4DAAK,WAAU;sEAAmB;;;;;;sEACnC,6LAAC;4DAAK,WAAU;sEAAY;;;;;;;iFAG9B;;sEACE,6LAAC;4DAAK,WAAU;sEAAmB;;;;;;sEACnC,6LAAC;4DAAK,WAAU;sEAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYpD;GA3KwB;KAAA", "debugId": null}}, {"offset": {"line": 997, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Development/My%20Projects/Portfolio/my-app/src/components/ui/navbar.tsx"], "sourcesContent": ["\"use client\";\r\nimport React, { useState, useRef, useCallback } from \"react\";\r\nimport FlipLink from \"./text-effect-flipper\";\r\nimport { useGS<PERSON> } from \"@gsap/react\";\r\nimport gsap from \"gsap\";\r\nimport ThemeToggleButton from \"./theme-toggle-button\";\r\nimport ContactMeButton from \"./contactMeButton\";\r\n\r\nconst MENU_ITEMS = [\r\n  { href: \"/\", label: \"Home\" },\r\n  { href: \"#about\", label: \"About\" },\r\n  { href: \"#projects\", label: \"Projects\" },\r\n  { href: \"#skills\", label: \"Skills\" },\r\n];\r\n\r\nconst MenuButton = ({ isOpen, onClick }) => (\r\n  <button\r\n    onClick={onClick}\r\n    className=\"text-2xl sm:text-3xl md:text-4xl hover:text-black-300 transition-colors p-2\"\r\n    aria-label=\"Toggle menu\"\r\n  >\r\n    {isOpen ? \"×\" : \"☰\"}\r\n  </button>\r\n);\r\n\r\n// eslint-disable-next-line react/display-name\r\nconst MenuItem = React.forwardRef(({ href, label }, ref) => (\r\n  <li ref={ref} className=\"relative group\">\r\n    <a\r\n      className=\"text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl\r\n             font-semibold inline-block\r\n             leading-normal py-2 sm:py-3 md:py-4\r\n             transition-all duration-300 ease-in-out\r\n             text-black dark:text-white\r\n             hover:text-red-800 dark:hover:text-red-400 hover:scale-110\r\n             font-[skiper] text-center\"\r\n    >\r\n      <FlipLink href={href}>{label}</FlipLink>\r\n    </a>\r\n    {/* Bottom border */}\r\n    <span\r\n      className=\"absolute left-0 bottom-0 w-0 h-[3px] bg-red-600 dark:bg-red-400\r\n                 transition-all duration-300 ease-in-out group-hover:w-full\"\r\n    ></span>\r\n  </li>\r\n));\r\n\r\nexport const Navbar = () => {\r\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\r\n  const [isMenuVisible, setIsMenuVisible] = useState(false); // Track visibility\r\n  const menuRef = useRef(null);\r\n  const menuItemsRef = useRef([]);\r\n\r\n  const openMenuAnimation = useCallback(() => {\r\n    gsap.fromTo(\r\n      menuRef.current,\r\n      { opacity: 0 },\r\n      { opacity: 1, duration: 0.5, ease: \"power2.inOut\" }\r\n    );\r\n\r\n    gsap.fromTo(\r\n      menuItemsRef.current,\r\n      { y: -50, opacity: 0 },\r\n      {\r\n        y: 0,\r\n        opacity: 1,\r\n        duration: 0.6,\r\n        stagger: 0.1,\r\n        ease: \"back.out(1.7)\",\r\n      }\r\n    );\r\n  }, []);\r\n\r\n  const closeMenuAnimation = useCallback(() => {\r\n    gsap.to(menuItemsRef.current, {\r\n      y: -50,\r\n      opacity: 0,\r\n      duration: 0.4,\r\n      stagger: 0.1,\r\n      ease: \"power2.in\",\r\n    });\r\n\r\n    gsap.to(menuRef.current, {\r\n      opacity: 0,\r\n      duration: 0.5,\r\n      ease: \"power2.inOut\",\r\n      delay: 0.3,\r\n      onComplete: () => {\r\n        setIsMenuVisible(false);\r\n      },\r\n    });\r\n  }, []);\r\n\r\n  const toggleMenu = useCallback(() => {\r\n    if (!isMenuOpen) {\r\n      setIsMenuVisible(true);\r\n      setIsMenuOpen(true);\r\n    } else {\r\n      setIsMenuOpen(false);\r\n      closeMenuAnimation();\r\n    }\r\n  }, [isMenuOpen, closeMenuAnimation]);\r\n\r\n  useGSAP(() => {\r\n    if (isMenuOpen) {\r\n      openMenuAnimation();\r\n    }\r\n  }, [isMenuOpen, openMenuAnimation]);\r\n\r\n  return (\r\n    <>\r\n      <div\r\n        className={`flex justify-between items-center px-4 sm:px-6 lg:px-8 py-3 sm:py-4\r\n                   fixed w-full top-0 z-50 transition-opacity duration-300 ${\r\n                     isMenuOpen\r\n                       ? \"opacity-0 pointer-events-none\"\r\n                       : \"opacity-100\"\r\n                   }`}\r\n      >\r\n        <div>\r\n          <ContactMeButton />\r\n        </div>\r\n\r\n        <div className=\"flex items-center gap-2 sm:gap-3 md:gap-4\">\r\n          <ThemeToggleButton />\r\n          <MenuButton isOpen={isMenuOpen} onClick={toggleMenu} />\r\n        </div>\r\n      </div>\r\n\r\n      {/* Menu button overlay - always visible with responsive positioning */}\r\n      <div className=\"fixed top-3 right-4 sm:top-4 sm:right-4 md:right-6 lg:right-8 z-[60]\">\r\n        <MenuButton isOpen={isMenuOpen} onClick={toggleMenu} />\r\n      </div>\r\n\r\n      {isMenuVisible && (\r\n        <div\r\n          ref={menuRef}\r\n          className=\"fixed inset-0 bg-white dark:bg-zinc-900 bg-opacity-95 dark:bg-opacity-95 z-40\r\n                     flex items-center justify-center px-4 sm:px-6 lg:px-8\"\r\n        >\r\n          <nav className=\"text-center w-full max-w-2xl\">\r\n            <ul className=\"flex flex-col gap-6 sm:gap-8 md:gap-10 lg:gap-12\">\r\n              {MENU_ITEMS.map((item, index) => (\r\n                <MenuItem\r\n                  key={item.href}\r\n                  href={item.href}\r\n                  label={item.label}\r\n                  ref={(el) => (menuItemsRef.current[index] = el)}\r\n                />\r\n              ))}\r\n            </ul>\r\n          </nav>\r\n        </div>\r\n      )}\r\n    </>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AACA;;;AANA;;;;;;;AAQA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAK,OAAO;IAAO;IAC3B;QAAE,MAAM;QAAU,OAAO;IAAQ;IACjC;QAAE,MAAM;QAAa,OAAO;IAAW;IACvC;QAAE,MAAM;QAAW,OAAO;IAAS;CACpC;AAED,MAAM,aAAa;QAAC,EAAE,MAAM,EAAE,OAAO,EAAE;yBACrC,6LAAC;QACC,SAAS;QACT,WAAU;QACV,cAAW;kBAEV,SAAS,MAAM;;;;;;;KANd;AAUN,8CAA8C;AAC9C,MAAM,yBAAW,6JAAA,CAAA,UAAK,CAAC,UAAU,CAAC,QAAkB;QAAjB,EAAE,IAAI,EAAE,KAAK,EAAE;yBAChD,6LAAC;QAAG,KAAK;QAAK,WAAU;;0BACtB,6LAAC;gBACC,WAAU;0BAQV,cAAA,6LAAC,wJAAA,CAAA,UAAQ;oBAAC,MAAM;8BAAO;;;;;;;;;;;0BAGzB,6LAAC;gBACC,WAAU;;;;;;;;;;;;;MAfV;AAqBC,MAAM,SAAS;;IACpB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ,mBAAmB;IAC9E,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACvB,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE,EAAE;IAE9B,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;iDAAE;YACpC,gJAAA,CAAA,UAAI,CAAC,MAAM,CACT,QAAQ,OAAO,EACf;gBAAE,SAAS;YAAE,GACb;gBAAE,SAAS;gBAAG,UAAU;gBAAK,MAAM;YAAe;YAGpD,gJAAA,CAAA,UAAI,CAAC,MAAM,CACT,aAAa,OAAO,EACpB;gBAAE,GAAG,CAAC;gBAAI,SAAS;YAAE,GACrB;gBACE,GAAG;gBACH,SAAS;gBACT,UAAU;gBACV,SAAS;gBACT,MAAM;YACR;QAEJ;gDAAG,EAAE;IAEL,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;kDAAE;YACrC,gJAAA,CAAA,UAAI,CAAC,EAAE,CAAC,aAAa,OAAO,EAAE;gBAC5B,GAAG,CAAC;gBACJ,SAAS;gBACT,UAAU;gBACV,SAAS;gBACT,MAAM;YACR;YAEA,gJAAA,CAAA,UAAI,CAAC,EAAE,CAAC,QAAQ,OAAO,EAAE;gBACvB,SAAS;gBACT,UAAU;gBACV,MAAM;gBACN,OAAO;gBACP,UAAU;8DAAE;wBACV,iBAAiB;oBACnB;;YACF;QACF;iDAAG,EAAE;IAEL,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;0CAAE;YAC7B,IAAI,CAAC,YAAY;gBACf,iBAAiB;gBACjB,cAAc;YAChB,OAAO;gBACL,cAAc;gBACd;YACF;QACF;yCAAG;QAAC;QAAY;KAAmB;IAEnC,CAAA,GAAA,kJAAA,CAAA,UAAO,AAAD;0BAAE;YACN,IAAI,YAAY;gBACd;YACF;QACF;yBAAG;QAAC;QAAY;KAAkB;IAElC,qBACE;;0BACE,6LAAC;gBACC,WAAW,AAAC,mJAKA,OAHC,aACI,kCACA;;kCAGjB,6LAAC;kCACC,cAAA,6LAAC,8IAAA,CAAA,UAAe;;;;;;;;;;kCAGlB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,wJAAA,CAAA,UAAiB;;;;;0CAClB,6LAAC;gCAAW,QAAQ;gCAAY,SAAS;;;;;;;;;;;;;;;;;;0BAK7C,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAW,QAAQ;oBAAY,SAAS;;;;;;;;;;;YAG1C,+BACC,6LAAC;gBACC,KAAK;gBACL,WAAU;0BAGV,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAG,WAAU;kCACX,WAAW,GAAG,CAAC,CAAC,MAAM,sBACrB,6LAAC;gCAEC,MAAM,KAAK,IAAI;gCACf,OAAO,KAAK,KAAK;gCACjB,KAAK,CAAC,KAAQ,aAAa,OAAO,CAAC,MAAM,GAAG;+BAHvC,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;AAYhC;GA7Ga;;QAwDX,kJAAA,CAAA,UAAO;;;MAxDI", "debugId": null}}]}