"use client";
import React, { useState, useEffect } from "react";

type FormState = "idle" | "loading" | "success";

export default function ContactMeButton() {
  const [formState, setFormState] = useState<FormState>("idle");
  const [open, setOpen] = useState(false);
  const [feedback, setFeedback] = useState("");

  function submit() {
    setFormState("loading");
    setTimeout(() => {
      setFormState("success");
    }, 1500);

    setTimeout(() => {
      setOpen(false);
      setFormState("idle");
      setFeedback("");
    }, 3300);
  }

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === "Escape") setOpen(false);

      if (
        (event.ctrlKey || event.metaKey) &&
        event.key === "Enter" &&
        open &&
        formState === "idle"
      ) {
        submit();
      }
    };

    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [open, formState]);

  return (
    <>
      {/* Contact Me Button */}
      <button
        onClick={() => setOpen(true)}
        className="px-2 sm:px-3 md:px-4 py-1 sm:py-2 text-sm sm:text-base md:text-lg
                    text-black bg-yellow-400
                   rounded-full transition-all duration-300 hover:bg-yellow-500
                   dark:text-gray-900 dark:bg-yellow-300 dark:hover:bg-yellow-400
                   transform hover:scale-105 hover:shadow-md active:scale-95
                   whitespace-nowrap font-[getInTouch]"
      >
        Get in Touch
      </button>

      {/* Popup */}
      {open && (
        <div
          className="fixed inset-0 z-50 flex items-center justify-center p-2 sm:p-4
                        bg-black/30 backdrop-blur-md"
        >
          {/* Overlay */}
          <div className="absolute inset-0" onClick={() => setOpen(false)} />

          {/* Modal */}
          <div
            className="relative bg-white dark:bg-zinc-900 rounded-xl sm:rounded-2xl shadow-2xl
                       w-full max-w-sm sm:max-w-md mx-2 sm:mx-4
                       animate-in zoom-in-95 slide-in-from-bottom-4 duration-300
                       border border-gray-200 dark:border-black"
          >
            {/* Header */}
            <div className="flex items-center justify-between p-4 sm:p-6 border-b border-gray-200 dark:border-gray-700">
              <div>
                <h2 className="text-xl font-bold text-gray-900 dark:text-white">
                  Get In Touch
                </h2>
                <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                  I'd love to hear from you! Send me a message.
                </p>
              </div>
              <button
                onClick={() => setOpen(false)}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 
                           w-8 h-8 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800"
              >
                ✕
              </button>
            </div>

            {formState === "success" ? (
              <div className="p-6 text-center">
                <div className="w-16 h-16 bg-green-100 dark:bg-green-800 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl">✅</span>
                </div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                  Message Sent Successfully!
                </h3>
                <p className="text-gray-600 dark:text-gray-400">
                  Thank you for reaching out! I'll get back to you within 24
                  hours.
                </p>
              </div>
            ) : (
              <form
                onSubmit={(e) => {
                  e.preventDefault();
                  if (!feedback.trim()) return;
                  submit();
                }}
                className="p-4 sm:p-6 space-y-3 sm:space-y-4"
              >
                <div>
                  <label className="block text-sm font-semibold text-gray-800 dark:text-gray-200 mb-2">
                    Your Message
                  </label>
                  <textarea
                    autoFocus
                    placeholder="Hi Parth! I'd like to discuss..."
                    value={feedback}
                    onChange={(e) => setFeedback(e.target.value)}
                    className="w-full h-32 px-3 py-2 border border-gray-300 dark:border-gray-700
                               rounded-lg focus:ring-2 focus:ring-yellow-400 focus:border-transparent
                               bg-white dark:bg-zinc-800 text-gray-900 dark:text-white
                               placeholder-gray-500 dark:placeholder-gray-400 transition-all"
                    required
                    minLength={10}
                  />
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    Minimum 10 characters required
                  </p>
                </div>

                <div className="flex flex-col sm:flex-row gap-2 sm:gap-3 pt-2">
                  <button
                    type="button"
                    onClick={() => setOpen(false)}
                    className="flex-1 px-3 sm:px-4 py-2 text-xs sm:text-sm font-semibold text-gray-700 dark:text-gray-300
                               bg-gray-100 dark:bg-gray-800 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-700
                               transition-colors duration-200"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    disabled={
                      formState === "loading" || feedback.trim().length < 10
                    }
                    className="flex-1 px-3 sm:px-4 py-2 text-xs sm:text-sm font-semibold text-black dark:text-gray-900
                               bg-yellow-400 dark:bg-yellow-300 rounded-lg hover:bg-yellow-500 dark:hover:bg-yellow-400
                               disabled:opacity-50 disabled:cursor-not-allowed
                               transition-all duration-200 transform hover:scale-105 active:scale-95
                               flex items-center justify-center gap-1 sm:gap-2"
                  >
                    {formState === "loading" ? (
                      <>
                        <div className="w-3 h-3 sm:w-4 sm:h-4 border-2 border-black dark:border-gray-900 border-t-transparent rounded-full animate-spin" />
                        <span className="hidden sm:inline">Sending...</span>
                        <span className="sm:hidden">...</span>
                      </>
                    ) : (
                      <>
                        <span className="hidden sm:inline">Send Message</span>
                        <span className="sm:hidden">Send</span>
                      </>
                    )}
                  </button>
                </div>
              </form>
            )}
          </div>
        </div>
      )}
    </>
  );
}
