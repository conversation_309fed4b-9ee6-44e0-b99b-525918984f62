{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Development/My%20Projects/Portfolio/my-app/src/components/ui/theme-provider.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { ThemeProvider as NextThemesProvider } from \"next-themes\"\nimport { type ThemeProviderProps } from \"next-themes/dist/types\"\n\nexport function ThemeProvider({ children, ...props }: ThemeProviderProps) {\n  return <NextThemesProvider {...props}>{children}</NextThemesProvider>\n}\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAMO,SAAS,cAAc,KAA0C;QAA1C,EAAE,QAAQ,EAAE,GAAG,OAA2B,GAA1C;IAC5B,qBAAO,6LAAC,mJAAA,CAAA,gBAAkB;QAAE,GAAG,KAAK;kBAAG;;;;;;AACzC;KAFgB", "debugId": null}}, {"offset": {"line": 38, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Development/My%20Projects/Portfolio/my-app/src/components/ui/preLoader.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect } from 'react';\r\nimport { motion, AnimatePresence } from 'framer-motion';\r\n\r\nconst words = [\r\n  \"Hello..!\", // English\r\n  \"こんにちは\", // Japanese\r\n  \"你好\", // Chinese\r\n  \"안녕하세요\", // Korean\r\n  \"नमस्ते\", // Hindi\r\n  \"مرحبا\", // Arabic\r\n  \"Hola\", // Spanish\r\n  \"Bonjour\", // French\r\n];\r\n\r\nexport default function Preloader() {\r\n  const [index, setIndex] = useState(0);\r\n  const [showLoader, setShowLoader] = useState(true);\r\n\r\n  useEffect(() => {\r\n    \r\n    const interval = setInterval(() => {\r\n      setIndex((prevIndex) => (prevIndex + 1) % words.length);\r\n    }, 600); \r\n\r\n    \r\n    const hideTimeout = setTimeout(() => {\r\n      setShowLoader(false);\r\n    }, 4000);\r\n\r\n    // Cleanup timers on component unmount\r\n    return () => {\r\n      clearInterval(interval);\r\n      clearTimeout(hideTimeout);\r\n    };\r\n  }, []);\r\n\r\n  // Variants for the text animation - slower transitions\r\n  const textVariants = {\r\n    initial: {\r\n      opacity: 0,\r\n      y: 20,\r\n    },\r\n    animate: {\r\n      opacity: 1,\r\n      y: 0,\r\n      transition: {\r\n        duration: 0.4,\r\n        ease: 'easeOut',\r\n      },\r\n    },\r\n    exit: {\r\n      opacity: 0,\r\n      y: -20,\r\n      transition: {\r\n        duration: 0.4,\r\n        ease: 'easeIn',\r\n      },\r\n    },\r\n  };\r\n  \r\n  // Variant for the loader container fade-out\r\n  const loaderVariants = {\r\n    exit: {\r\n      opacity: 0,\r\n      transition: {\r\n        duration: 0.8,\r\n        ease: 'easeInOut',\r\n      },\r\n    },\r\n  };\r\n\r\n  return (\r\n    <AnimatePresence>\r\n      {showLoader && (\r\n        <motion.div\r\n          className=\"fixed inset-0 z-60 flex items-center justify-center bg-white dark:bg-black\"\r\n          variants={loaderVariants}\r\n          exit=\"exit\"\r\n        >\r\n          <div className=\"relative\">\r\n            <div className=\"absolute inset-0 -z-10 blur-3xl bg-gradient-to-r from-pink-500/30 via-purple-500/30 to-cyan-500/30\" />\r\n            <AnimatePresence mode=\"wait\">\r\n              <motion.h1\r\n                key={words[index]}\r\n                className=\"text-4xl font-serif text-gray-700 dark:text-gray-300 md:text-8xl\"\r\n                variants={textVariants}\r\n                initial=\"initial\"\r\n                animate=\"animate\"\r\n                exit=\"exit\"\r\n              >\r\n                {words[index]}\r\n              </motion.h1>\r\n            </AnimatePresence>\r\n          </div>\r\n        </motion.div>\r\n      )}\r\n    </AnimatePresence>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;;;AAHA;;;AAKA,MAAM,QAAQ;IACZ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAEc,SAAS;;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YAER,MAAM,WAAW;gDAAY;oBAC3B;wDAAS,CAAC,YAAc,CAAC,YAAY,CAAC,IAAI,MAAM,MAAM;;gBACxD;+CAAG;YAGH,MAAM,cAAc;mDAAW;oBAC7B,cAAc;gBAChB;kDAAG;YAEH,sCAAsC;YACtC;uCAAO;oBACL,cAAc;oBACd,aAAa;gBACf;;QACF;8BAAG,EAAE;IAEL,uDAAuD;IACvD,MAAM,eAAe;QACnB,SAAS;YACP,SAAS;YACT,GAAG;QACL;QACA,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBACV,UAAU;gBACV,MAAM;YACR;QACF;QACA,MAAM;YACJ,SAAS;YACT,GAAG,CAAC;YACJ,YAAY;gBACV,UAAU;gBACV,MAAM;YACR;QACF;IACF;IAEA,4CAA4C;IAC5C,MAAM,iBAAiB;QACrB,MAAM;YACJ,SAAS;YACT,YAAY;gBACV,UAAU;gBACV,MAAM;YACR;QACF;IACF;IAEA,qBACE,6LAAC,4LAAA,CAAA,kBAAe;kBACb,4BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;YACT,WAAU;YACV,UAAU;YACV,MAAK;sBAEL,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC,4LAAA,CAAA,kBAAe;wBAAC,MAAK;kCACpB,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;4BAER,WAAU;4BACV,UAAU;4BACV,SAAQ;4BACR,SAAQ;4BACR,MAAK;sCAEJ,KAAK,CAAC,MAAM;2BAPR,KAAK,CAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;AAejC;GApFwB;KAAA", "debugId": null}}]}