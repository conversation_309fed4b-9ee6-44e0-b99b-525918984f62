'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

const words = [
  "Hello..!", // English
  "こんにちは", // Japanese
  "你好", // Chinese
  "안녕하세요", // Korean
  "नमस्ते", // Hindi
  "مرحبا", // Arabic
  "Hola", // Spanish
  "Bonjour", // French
];

export default function Preloader() {
  const [index, setIndex] = useState(0);
  const [showLoader, setShowLoader] = useState(true);

  useEffect(() => {
    
    const interval = setInterval(() => {
      setIndex((prevIndex) => (prevIndex + 1) % words.length);
    }, 600); 

    
    const hideTimeout = setTimeout(() => {
      setShowLoader(false);
    }, 4000);

    // Cleanup timers on component unmount
    return () => {
      clearInterval(interval);
      clearTimeout(hideTimeout);
    };
  }, []);

  // Variants for the text animation - slower transitions
  const textVariants = {
    initial: {
      opacity: 0,
      y: 20,
    },
    animate: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.4,
        ease: 'easeOut',
      },
    },
    exit: {
      opacity: 0,
      y: -20,
      transition: {
        duration: 0.4,
        ease: 'easeIn',
      },
    },
  };
  
  // Variant for the loader container fade-out
  const loaderVariants = {
    exit: {
      opacity: 0,
      transition: {
        duration: 0.8,
        ease: 'easeInOut',
      },
    },
  };

  return (
    <AnimatePresence>
      {showLoader && (
        <motion.div
          className="fixed inset-0 z-60 flex items-center justify-center bg-white dark:bg-black"
          variants={loaderVariants}
          exit="exit"
        >
          <div className="relative">
            <div className="absolute inset-0 -z-10 blur-3xl bg-gradient-to-r from-pink-500/30 via-purple-500/30 to-cyan-500/30" />
            <AnimatePresence mode="wait">
              <motion.h1
                key={words[index]}
                className="text-4xl font-serif text-gray-700 dark:text-gray-300 md:text-8xl"
                variants={textVariants}
                initial="initial"
                animate="animate"
                exit="exit"
              >
                {words[index]}
              </motion.h1>
            </AnimatePresence>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}