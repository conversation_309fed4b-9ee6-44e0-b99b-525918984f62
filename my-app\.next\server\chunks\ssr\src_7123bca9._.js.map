{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Development/My%20Projects/Portfolio/my-app/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 19, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Development/My%20Projects/Portfolio/my-app/src/components/ui/heroSectionBG.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport { motion } from \"framer-motion\"\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction ElegantShape({\r\n  className,\r\n  delay = 0,\r\n  width = 400,\r\n  height = 100,\r\n  rotate = 0,\r\n  gradient = \"from-white/[0.08]\",\r\n}: {\r\n  className?: string\r\n  delay?: number\r\n  width?: number\r\n  height?: number\r\n  rotate?: number\r\n  gradient?: string\r\n}) {\r\n  return (\r\n    <motion.div\r\n      initial={{\r\n        opacity: 0,\r\n        y: -150,\r\n        rotate: rotate - 15,\r\n      }}\r\n      animate={{\r\n        opacity: 1,\r\n        y: 0,\r\n        rotate: rotate,\r\n      }}\r\n      transition={{\r\n        duration: 2.4,\r\n        delay,\r\n        ease: [0.23, 0.86, 0.39, 0.96],\r\n        opacity: { duration: 1.2 },\r\n      }}\r\n      className={cn(\"absolute\", className)}\r\n    >\r\n      <motion.div\r\n        animate={{\r\n          y: [0, 15, 0],\r\n        }}\r\n        transition={{\r\n          duration: 12,\r\n          repeat: Number.POSITIVE_INFINITY,\r\n          ease: \"easeInOut\",\r\n        }}\r\n        style={{\r\n          width,\r\n          height,\r\n        }}\r\n        className=\"relative\"\r\n      >\r\n        <div\r\n          className={cn(\r\n            \"absolute inset-0 rounded-full\",\r\n            \"bg-gradient-to-r to-transparent\",\r\n            gradient,\r\n            \"backdrop-blur-[2px] border-2 border-white/[0.15] dark:border-white/[0.15] border-black/[0.15]\",\r\n            \"shadow-[0_8px_32px_0_rgba(0,0,0,0.1)] dark:shadow-[0_8px_32px_0_rgba(255,255,255,0.1)]\",\r\n            \"after:absolute after:inset-0 after:rounded-full\",\r\n            \"after:bg-[radial-gradient(circle_at_50%_50%,rgba(0,0,0,0.2),transparent_70%)] dark:after:bg-[radial-gradient(circle_at_50%_50%,rgba(255,255,255,0.2),transparent_70%)]\",\r\n          )}\r\n        />\r\n      </motion.div>\r\n    </motion.div>\r\n  )\r\n}\r\n\r\nexport default function HeroSectionBG() {\r\n  return (\r\n    <div className=\"absolute min-h-screen w-full flex items-center justify-center overflow-hidden bg-white dark:bg-[#030303]\">\r\n      <div className=\"absolute inset-0 bg-gradient-to-br from-indigo-500/[0.05] via-transparent to-rose-500/[0.05] blur-3xl\" />\r\n\r\n      <div className=\"absolute inset-0 overflow-hidden\">\r\n        <ElegantShape\r\n          delay={0.3}\r\n          width={600}\r\n          height={140}\r\n          rotate={12}\r\n          gradient=\"from-indigo-500/[0.15]\"\r\n          className=\"left-[-10%] md:left-[-5%] top-[15%] md:top-[20%]\"\r\n        />\r\n\r\n        <ElegantShape\r\n          delay={0.5}\r\n          width={500}\r\n          height={120}\r\n          rotate={-15}\r\n          gradient=\"from-rose-500/[0.15]\"\r\n          className=\"right-[-5%] md:right-[0%] top-[70%] md:top-[75%]\"\r\n        />\r\n\r\n        <ElegantShape\r\n          delay={0.4}\r\n          width={300}\r\n          height={80}\r\n          rotate={-8}\r\n          gradient=\"from-violet-500/[0.15]\"\r\n          className=\"left-[5%] md:left-[10%] bottom-[5%] md:bottom-[10%]\"\r\n        />\r\n\r\n        <ElegantShape\r\n          delay={0.6}\r\n          width={200}\r\n          height={60}\r\n          rotate={20}\r\n          gradient=\"from-amber-500/[0.15]\"\r\n          className=\"right-[15%] md:right-[20%] top-[10%] md:top-[15%]\"\r\n        />\r\n\r\n        <ElegantShape\r\n          delay={0.7}\r\n          width={150}\r\n          height={40}\r\n          rotate={-25}\r\n          gradient=\"from-cyan-500/[0.15]\"\r\n          className=\"left-[20%] md:left-[25%] top-[5%] md:top-[10%]\"\r\n        />\r\n      </div>\r\n\r\n      <div className=\"absolute inset-0 bg-gradient-to-t from-white via-transparent to-white/80 dark:from-[#030303] dark:via-transparent dark:to-[#030303]/80 pointer-events-none\" />\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKA,SAAS,aAAa,EACpB,SAAS,EACT,QAAQ,CAAC,EACT,QAAQ,GAAG,EACX,SAAS,GAAG,EACZ,SAAS,CAAC,EACV,WAAW,mBAAmB,EAQ/B;IACC,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YACP,SAAS;YACT,GAAG,CAAC;YACJ,QAAQ,SAAS;QACnB;QACA,SAAS;YACP,SAAS;YACT,GAAG;YACH,QAAQ;QACV;QACA,YAAY;YACV,UAAU;YACV;YACA,MAAM;gBAAC;gBAAM;gBAAM;gBAAM;aAAK;YAC9B,SAAS;gBAAE,UAAU;YAAI;QAC3B;QACA,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;kBAE1B,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YACT,SAAS;gBACP,GAAG;oBAAC;oBAAG;oBAAI;iBAAE;YACf;YACA,YAAY;gBACV,UAAU;gBACV,QAAQ,OAAO,iBAAiB;gBAChC,MAAM;YACR;YACA,OAAO;gBACL;gBACA;YACF;YACA,WAAU;sBAEV,cAAA,8OAAC;gBACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iCACA,mCACA,UACA,iGACA,0FACA,mDACA;;;;;;;;;;;;;;;;AAMZ;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;;;;;0BAEf,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,OAAO;wBACP,OAAO;wBACP,QAAQ;wBACR,QAAQ;wBACR,UAAS;wBACT,WAAU;;;;;;kCAGZ,8OAAC;wBACC,OAAO;wBACP,OAAO;wBACP,QAAQ;wBACR,QAAQ,CAAC;wBACT,UAAS;wBACT,WAAU;;;;;;kCAGZ,8OAAC;wBACC,OAAO;wBACP,OAAO;wBACP,QAAQ;wBACR,QAAQ,CAAC;wBACT,UAAS;wBACT,WAAU;;;;;;kCAGZ,8OAAC;wBACC,OAAO;wBACP,OAAO;wBACP,QAAQ;wBACR,QAAQ;wBACR,UAAS;wBACT,WAAU;;;;;;kCAGZ,8OAAC;wBACC,OAAO;wBACP,OAAO;wBACP,QAAQ;wBACR,QAAQ,CAAC;wBACT,UAAS;wBACT,WAAU;;;;;;;;;;;;0BAId,8OAAC;gBAAI,WAAU;;;;;;;;;;;;AAGrB", "debugId": null}}, {"offset": {"line": 189, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Development/My%20Projects/Portfolio/my-app/src/components/ui/homePage.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\";\r\nimport Image from \"next/image\";\r\nimport FlipLink from \"@/components/ui/text-effect-flipper\";\r\nimport dynamic from \"next/dynamic\";\r\n\r\nconst HomePage = () => {\r\n  return (\r\n    <div className=\"relative min-h-screen overflow-hidden\">\r\n      <h1 className=\"text-6xl flex items-center justify-center mt-100 font-bold  \">\r\n        I&apos;m\r\n        <Image\r\n          src=\"/profilePhoto.jpg\"\r\n          alt=\"Profile photo of Parth Chauhan\"\r\n          width={80}\r\n          height={80}\r\n          className=\"w-20 h-15 rounded-xl  mx-2 object-cover\"\r\n        />\r\n        Parth Chauhan\r\n      </h1>\r\n      <h2 className=\"text-7xl flex items-center justify-center mt-4 font-bold font-[skiper] \">\r\n        Bringing Ideas to\r\n        <pre> </pre>\r\n        <span className=\"text-black bg-yellow-300 px-2 py-1 rounded-lg\">Reality</span>\r\n      </h2>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default HomePage;\r\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAOA,MAAM,WAAW;IACf,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAG,WAAU;;oBAA+D;kCAE3E,8OAAC,6HAAA,CAAA,UAAK;wBACJ,KAAI;wBACJ,KAAI;wBACJ,OAAO;wBACP,QAAQ;wBACR,WAAU;;;;;;oBACV;;;;;;;0BAGJ,8OAAC;gBAAG,WAAU;;oBAA0E;kCAEtF,8OAAC;kCAAI;;;;;;kCACL,8OAAC;wBAAK,WAAU;kCAAgD;;;;;;;;;;;;;;;;;;AAIxE;uCAEe", "debugId": null}}, {"offset": {"line": 260, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Development/My%20Projects/Portfolio/my-app/src/components/ui/text-effect-flipper.tsx"], "sourcesContent": ["\"use client\";\n\nimport React from \"react\";\nimport { motion } from \"framer-motion\";\n\nconst DURATION = 0.30;\nconst STAGGER = 0.025;\n\ninterface FlipLinkProps {\n  children: string;\n  href: string;\n}\n\nconst FlipLink: React.FC<FlipLinkProps> = ({ children, href }) => {\n  return (\n    <motion.a\n      initial=\"initial\"\n      whileHover=\"hovered\"\n      target=\"_blank\"\n      href={href}\n      className=\"relative block overflow-hidden whitespace-nowrap text-4xl font-semibold uppercase dark:text-white/90 sm:text-7xl md:text-8xl \"\n      style={{\n        lineHeight: 0.75,\n      }}\n    >\n      <div>\n        {children.split(\"\").map((l, i) => (\n          <motion.span\n            variants={{\n              initial: {\n                y: 0,\n              },\n              hovered: {\n                y: \"-100%\",\n              },\n            }}\n            transition={{\n              duration: DURATION,\n              ease: \"easeInOut\",\n              delay: STAGGER * i,\n            }}\n            className=\"inline-block\"\n            key={i}\n          >\n            {l}\n          </motion.span>\n        ))}\n      </div>\n      <div className=\"absolute inset-0\">\n        {children.split(\"\").map((l, i) => (\n          <motion.span\n            variants={{\n              initial: {\n                y: \"100%\",\n              },\n              hovered: {\n                y: 0,\n              },\n            }}\n            transition={{\n              duration: DURATION,\n              ease: \"easeInOut\",\n              delay: STAGGER * i,\n            }}\n            className=\"inline-block\"\n            key={i}\n          >\n            {l}\n          </motion.span>\n        ))}\n      </div>\n    </motion.a>\n  );\n};\n\nexport default FlipLink;\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAKA,MAAM,WAAW;AACjB,MAAM,UAAU;AAOhB,MAAM,WAAoC,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE;IAC3D,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;QACP,SAAQ;QACR,YAAW;QACX,QAAO;QACP,MAAM;QACN,WAAU;QACV,OAAO;YACL,YAAY;QACd;;0BAEA,8OAAC;0BACE,SAAS,KAAK,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,kBAC1B,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;wBACV,UAAU;4BACR,SAAS;gCACP,GAAG;4BACL;4BACA,SAAS;gCACP,GAAG;4BACL;wBACF;wBACA,YAAY;4BACV,UAAU;4BACV,MAAM;4BACN,OAAO,UAAU;wBACnB;wBACA,WAAU;kCAGT;uBAFI;;;;;;;;;;0BAMX,8OAAC;gBAAI,WAAU;0BACZ,SAAS,KAAK,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,kBAC1B,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;wBACV,UAAU;4BACR,SAAS;gCACP,GAAG;4BACL;4BACA,SAAS;gCACP,GAAG;4BACL;wBACF;wBACA,YAAY;4BACV,UAAU;4BACV,MAAM;4BACN,OAAO,UAAU;wBACnB;wBACA,WAAU;kCAGT;uBAFI;;;;;;;;;;;;;;;;AAQjB;uCAEe", "debugId": null}}, {"offset": {"line": 348, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Development/My%20Projects/Portfolio/my-app/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 403, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Development/My%20Projects/Portfolio/my-app/src/components/ui/theme-animations.ts"], "sourcesContent": ["export type AnimationVariant = \"circle\" | \"circle-blur\" | \"polygon\" | \"gif\"\nexport type AnimationStart =\n  | \"top-left\"\n  | \"top-right\"\n  | \"bottom-left\"\n  | \"bottom-right\"\n  | \"center\"\n\ninterface Animation {\n  name: string\n  css: string\n}\n\nconst getPositionCoords = (position: AnimationStart) => {\n  switch (position) {\n    case \"top-left\":\n      return { cx: \"0\", cy: \"0\" }\n    case \"top-right\":\n      return { cx: \"40\", cy: \"0\" }\n    case \"bottom-left\":\n      return { cx: \"0\", cy: \"40\" }\n    case \"bottom-right\":\n      return { cx: \"40\", cy: \"40\" }\n  }\n}\n\nconst generateSVG = (variant: AnimationVariant, start: AnimationStart) => {\n  if (start === \"center\") return\n\n  const positionCoords = getPositionCoords(start)\n  if (!positionCoords) {\n    throw new Error(`Invalid start position: ${start}`)\n  }\n  const { cx, cy } = positionCoords\n\n  if (variant === \"circle\") {\n    return `data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 40 40\"><circle cx=\"${cx}\" cy=\"${cy}\" r=\"20\" fill=\"white\"/></svg>`\n  }\n\n  if (variant === \"circle-blur\") {\n    return `data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 40 40\"><defs><filter id=\"blur\"><feGaussianBlur stdDeviation=\"2\"/></filter></defs><circle cx=\"${cx}\" cy=\"${cy}\" r=\"18\" fill=\"white\" filter=\"url(%23blur)\"/></svg>`\n  }\n\n  return \"\"\n}\n\nconst getTransformOrigin = (start: AnimationStart) => {\n  switch (start) {\n    case \"top-left\":\n      return \"top left\"\n    case \"top-right\":\n      return \"top right\"\n    case \"bottom-left\":\n      return \"bottom left\"\n    case \"bottom-right\":\n      return \"bottom right\"\n  }\n}\n\nexport const createAnimation = (\n  variant: AnimationVariant,\n  start: AnimationStart,\n  url?: string\n): Animation => {\n  const svg = generateSVG(variant, start)\n  const transformOrigin = getTransformOrigin(start)\n\n  if (variant === \"polygon\") {\n    return {\n      name: `${variant}-${start}`,\n      css: `\n       ::view-transition-group(root) {\n        animation-duration: 0.7s;\n        animation-timing-function: var(--expo-out);\n      }\n            \n      ::view-transition-new(root) {\n        animation-name: reveal-light;\n      }\n\n      ::view-transition-old(root),\n      .dark::view-transition-old(root) {\n        animation: none;\n        z-index: -1;\n      }\n      .dark::view-transition-new(root) {\n        animation-name: reveal-dark;\n      }\n\n      @keyframes reveal-dark {\n        from {\n          clip-path: polygon(50% -71%, -50% 71%, -50% 71%, 50% -71%);\n        }\n        to {\n          clip-path: polygon(50% -71%, -50% 71%, 50% 171%, 171% 50%);\n        }\n      }\n\n      @keyframes reveal-light {\n        from {\n          clip-path: polygon(171% 50%, 50% 171%, 50% 171%, 171% 50%);\n        }\n        to {\n          clip-path: polygon(171% 50%, 50% 171%, -50% 71%, 50% -71%);\n        }\n      }\n      `,\n    }\n  }\n  if (variant === \"circle\" && start == \"center\") {\n    return {\n      name: `${variant}-${start}`,\n      css: `\n       ::view-transition-group(root) {\n        animation-duration: 0.7s;\n        animation-timing-function: var(--expo-out);\n      }\n            \n      ::view-transition-new(root) {\n        animation-name: reveal-light;\n      }\n\n      ::view-transition-old(root),\n      .dark::view-transition-old(root) {\n        animation: none;\n        z-index: -1;\n      }\n      .dark::view-transition-new(root) {\n        animation-name: reveal-dark;\n      }\n\n      @keyframes reveal-dark {\n        from {\n          clip-path: circle(0% at 50% 50%);\n        }\n        to {\n          clip-path: circle(100.0% at 50% 50%);\n        }\n      }\n\n      @keyframes reveal-light {\n        from {\n           clip-path: circle(0% at 50% 50%);\n        }\n        to {\n          clip-path: circle(100.0% at 50% 50%);\n        }\n      }\n      `,\n    }\n  }\n  if (variant === \"gif\") {\n    return {\n      name: `${variant}-${start}`,\n      css: `\n      ::view-transition-group(root) {\n  animation-timing-function: var(--expo-in);\n}\n\n::view-transition-new(root) {\n  mask: url('${url}') center / 0 no-repeat;\n  animation: scale 3s;\n}\n\n::view-transition-old(root),\n.dark::view-transition-old(root) {\n  animation: scale 3s;\n}\n\n@keyframes scale {\n  0% {\n    mask-size: 0;\n  }\n  10% {\n    mask-size: 50vmax;\n  }\n  90% {\n    mask-size: 50vmax;\n  }\n  100% {\n    mask-size: 2000vmax;\n  }\n}`,\n    }\n  }\n\n  return {\n    name: `${variant}-${start}`,\n    css: `\n      ::view-transition-group(root) {\n        animation-timing-function: var(--expo-out);\n      }\n      ::view-transition-new(root) {\n        mask: url('${svg}') ${start.replace(\"-\", \" \")} / 0 no-repeat;\n        mask-origin: content-box;\n        animation: scale-${start} 1s;\n        transform-origin: ${transformOrigin};\n      }\n      ::view-transition-old(root),\n      .dark::view-transition-old(root) {\n        animation: scale-${start} 1s;\n        transform-origin: ${transformOrigin};\n        z-index: -1;\n      }\n      @keyframes scale-${start} {\n        to {\n          mask-size: 350vmax;\n        }\n      }\n    `,\n  }\n}\n"], "names": [], "mappings": ";;;AAaA,MAAM,oBAAoB,CAAC;IACzB,OAAQ;QACN,KAAK;YACH,OAAO;gBAAE,IAAI;gBAAK,IAAI;YAAI;QAC5B,KAAK;YACH,OAAO;gBAAE,IAAI;gBAAM,IAAI;YAAI;QAC7B,KAAK;YACH,OAAO;gBAAE,IAAI;gBAAK,IAAI;YAAK;QAC7B,KAAK;YACH,OAAO;gBAAE,IAAI;gBAAM,IAAI;YAAK;IAChC;AACF;AAEA,MAAM,cAAc,CAAC,SAA2B;IAC9C,IAAI,UAAU,UAAU;IAExB,MAAM,iBAAiB,kBAAkB;IACzC,IAAI,CAAC,gBAAgB;QACnB,MAAM,IAAI,MAAM,CAAC,wBAAwB,EAAE,OAAO;IACpD;IACA,MAAM,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG;IAEnB,IAAI,YAAY,UAAU;QACxB,OAAO,CAAC,2FAA2F,EAAE,GAAG,MAAM,EAAE,GAAG,6BAA6B,CAAC;IACnJ;IAEA,IAAI,YAAY,eAAe;QAC7B,OAAO,CAAC,qKAAqK,EAAE,GAAG,MAAM,EAAE,GAAG,mDAAmD,CAAC;IACnP;IAEA,OAAO;AACT;AAEA,MAAM,qBAAqB,CAAC;IAC1B,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;IACX;AACF;AAEO,MAAM,kBAAkB,CAC7B,SACA,OACA;IAEA,MAAM,MAAM,YAAY,SAAS;IACjC,MAAM,kBAAkB,mBAAmB;IAE3C,IAAI,YAAY,WAAW;QACzB,OAAO;YACL,MAAM,GAAG,QAAQ,CAAC,EAAE,OAAO;YAC3B,KAAK,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;MAoCN,CAAC;QACH;IACF;IACA,IAAI,YAAY,YAAY,SAAS,UAAU;QAC7C,OAAO;YACL,MAAM,GAAG,QAAQ,CAAC,EAAE,OAAO;YAC3B,KAAK,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;MAoCN,CAAC;QACH;IACF;IACA,IAAI,YAAY,OAAO;QACrB,OAAO;YACL,MAAM,GAAG,QAAQ,CAAC,EAAE,OAAO;YAC3B,KAAK,CAAC;;;;;;aAMC,EAAE,IAAI;;;;;;;;;;;;;;;;;;;;;;CAsBlB,CAAC;QACE;IACF;IAEA,OAAO;QACL,MAAM,GAAG,QAAQ,CAAC,EAAE,OAAO;QAC3B,KAAK,CAAC;;;;;mBAKS,EAAE,IAAI,GAAG,EAAE,MAAM,OAAO,CAAC,KAAK,KAAK;;yBAE7B,EAAE,MAAM;0BACP,EAAE,gBAAgB;;;;yBAInB,EAAE,MAAM;0BACP,EAAE,gBAAgB;;;uBAGrB,EAAE,MAAM;;;;;IAK3B,CAAC;IACH;AACF", "debugId": null}}, {"offset": {"line": 608, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Development/My%20Projects/Portfolio/my-app/src/components/ui/theme-toggle-button.tsx"], "sourcesContent": ["\"use client\"\n\nimport React from \"react\"\nimport { MoonIcon, SunIcon } from \"lucide-react\"\nimport { useTheme } from \"next-themes\"\n\nimport { Button } from \"@/components/ui/button\"\n\nimport {\n  AnimationStart,\n  AnimationVariant,\n  createAnimation,\n} from \"./theme-animations\"\n\ninterface ThemeToggleAnimationProps {\n  variant?: AnimationVariant\n  start?: AnimationStart\n  showLabel?: boolean\n  url?: string\n}\n\nexport default function ThemeToggleButton({\n  variant = \"circle-blur\",\n  start = \"top-left\",\n  showLabel = false,\n  url = \"\",\n}: ThemeToggleAnimationProps) {\n  const { theme, setTheme } = useTheme()\n\n  const styleId = \"theme-transition-styles\"\n\n  const updateStyles = React.useCallback((css: string, name: string) => {\n    if (typeof window === \"undefined\") return\n\n    let styleElement = document.getElementById(styleId) as HTMLStyleElement\n\n    console.log(\"style ELement\", styleElement)\n    console.log(\"name\", name)\n\n    if (!styleElement) {\n      styleElement = document.createElement(\"style\")\n      styleElement.id = styleId\n      document.head.appendChild(styleElement)\n    }\n\n    styleElement.textContent = css\n\n    console.log(\"content updated\")\n  }, [])\n\n  const toggleTheme = React.useCallback(() => {\n    const animation = createAnimation(variant, start, url)\n\n    updateStyles(animation.css, animation.name)\n\n    if (typeof window === \"undefined\") return\n\n    const switchTheme = () => {\n      setTheme(theme === \"light\" ? \"dark\" : \"light\")\n    }\n\n    if (!document.startViewTransition) {\n      switchTheme()\n      return\n    }\n\n    document.startViewTransition(switchTheme)\n  }, [theme, setTheme])\n\n  return (\n    <Button\n      onClick={toggleTheme}\n      variant=\"ghost\"\n      size=\"icon\"\n      className=\"w-9 p-0 h-9 relative group\"\n      name=\"Theme Toggle Button\"\n    >\n      <SunIcon className=\"size-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0\" />\n      <MoonIcon className=\"absolute size-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100\" />\n      <span className=\"sr-only\">Theme Toggle </span>\n      {showLabel && (\n        <>\n          <span className=\"hidden group-hover:block border rounded-full px-2 absolute -top-10\">\n            {\" \"}\n            variant = {variant}\n          </span>\n          <span className=\"hidden group-hover:block border rounded-full px-2 absolute -bottom-10\">\n            {\" \"}\n            start = {start}\n          </span>\n        </>\n      )}\n    </Button>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAEA;AAEA;AARA;;;;;;;AAqBe,SAAS,kBAAkB,EACxC,UAAU,aAAa,EACvB,QAAQ,UAAU,EAClB,YAAY,KAAK,EACjB,MAAM,EAAE,EACkB;IAC1B,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD;IAEnC,MAAM,UAAU;IAEhB,MAAM,eAAe,qMAAA,CAAA,UAAK,CAAC,WAAW,CAAC,CAAC,KAAa;QACnD,wCAAmC;;;QAEnC,IAAI;IAcN,GAAG,EAAE;IAEL,MAAM,cAAc,qMAAA,CAAA,UAAK,CAAC,WAAW,CAAC;QACpC,MAAM,YAAY,CAAA,GAAA,8IAAA,CAAA,kBAAe,AAAD,EAAE,SAAS,OAAO;QAElD,aAAa,UAAU,GAAG,EAAE,UAAU,IAAI;QAE1C,wCAAmC;;;QAEnC,MAAM;IAUR,GAAG;QAAC;QAAO;KAAS;IAEpB,qBACE,8OAAC,kIAAA,CAAA,SAAM;QACL,SAAS;QACT,SAAQ;QACR,MAAK;QACL,WAAU;QACV,MAAK;;0BAEL,8OAAC,oMAAA,CAAA,UAAO;gBAAC,WAAU;;;;;;0BACnB,8OAAC,sMAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;0BACpB,8OAAC;gBAAK,WAAU;0BAAU;;;;;;YACzB,2BACC;;kCACE,8OAAC;wBAAK,WAAU;;4BACb;4BAAI;4BACM;;;;;;;kCAEb,8OAAC;wBAAK,WAAU;;4BACb;4BAAI;4BACI;;;;;;;;;;;;;;;AAMrB", "debugId": null}}, {"offset": {"line": 713, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Development/My%20Projects/Portfolio/my-app/src/components/ui/popover-form.tsx"], "sourcesContent": ["\"use client\"\n\nimport { <PERSON>actNode, RefObject, useEffect, useRef } from \"react\"\nimport { ChevronUp, Loader } from \"lucide-react\"\nimport { AnimatePresence, motion } from \"framer-motion\";\n\n\ntype PopoverFormProps = {\n  open: boolean\n  setOpen: (open: boolean) => void\n  openChild?: ReactNode\n  successChild?: ReactNode\n  showSuccess: boolean\n  width?: string\n  height?: string\n  showCloseButton?: boolean\n  title: string\n}\n\nexport function PopoverForm({\n  open,\n  setOpen,\n  openChild,\n  showSuccess,\n  successChild,\n  width = \"364px\",\n  height = \"192px\",\n  title = \"Feedback\",\n  showCloseButton = false,\n}: PopoverFormProps) {\n  const ref = useRef<HTMLDivElement>(null)\n  useClickOutside(ref, () => setOpen(false))\n\n  return (\n    <div\n      key={title}\n      className=\"flex min-h-[300px] w-full items-center justify-center\"\n    >\n      <motion.button\n        layoutId={`${title}-wrapper`}\n        onClick={() => setO<PERSON>(true)}\n        style={{ borderRadius: 8 }}\n        className=\"flex h-9 items-center border bg-white dark:bg-[#121212] px-3 text-sm font-medium outline-none\"\n      >\n        <motion.span layoutId={`${title}-title`}>{title}</motion.span>\n      </motion.button>\n      <AnimatePresence>\n        {open && (\n          <motion.div\n            layoutId={`${title}-wrapper`}\n            className=\"absolute p-1 overflow-hidden bg-muted shadow-[0_0_0_1px_rgba(0,0,0,0.08),0px_1px_2px_rgba(0,0,0,0.04)] outline-none\"\n            ref={ref}\n            style={{ borderRadius: 10, width, height }}\n          >\n            <motion.span\n              aria-hidden\n              className=\"absolute left-4 top-[17px] text-sm text-muted-foreground data-[success]:text-transparent\"\n              layoutId={`${title}-title`}\n              data-success={showSuccess}\n            >\n              {title}\n            </motion.span>\n\n            {showCloseButton && (\n              <div className=\"absolute -top-[5px] left-1/2 transform -translate-x-1/2 w-[12px] h-[26px] flex items-center justify-center z-20\">\n                <button\n                  onClick={() => setOpen(false)}\n                  className=\"absolute z-10 -mt-1 flex items-center justify-center w-[10px] h-[6px] text-muted-foreground hover:text-foreground focus:outline-none  rounded-full \"\n                  aria-label=\"Close\"\n                >\n                  <ChevronUp className=\"text-muted-foreground/80\" />\n                </button>\n\n                <PopoverFormCutOutTopIcon />\n              </div>\n            )}\n\n            <AnimatePresence mode=\"popLayout\">\n              {showSuccess ? (\n                <motion.div\n                  key=\"success\"\n                  initial={{ y: -32, opacity: 0, filter: \"blur(4px)\" }}\n                  animate={{ y: 0, opacity: 1, filter: \"blur(0px)\" }}\n                  transition={{ type: \"spring\", duration: 0.4, bounce: 0 }}\n                  className=\"flex h-full flex-col items-center justify-center\"\n                >\n                  {successChild || <PopoverFormSuccess />}\n                </motion.div>\n              ) : (\n                <motion.div\n                  exit={{ y: 8, opacity: 0, filter: \"blur(4px)\" }}\n                  transition={{ type: \"spring\", duration: 0.4, bounce: 0 }}\n                  key=\"open-child\"\n                  style={{ borderRadius: 10 }}\n                  className=\"h-full border bg-white dark:bg-[#121212] z-20 \"\n                >\n                  {openChild}\n                </motion.div>\n              )}\n            </AnimatePresence>\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </div>\n  )\n}\n\nexport function PopoverFormButton({\n  loading,\n  text = \"submit\",\n}: {\n  loading: boolean\n  text: string\n}) {\n  return (\n    <button\n      type=\"submit\"\n      className=\"ml-auto flex h-6 w-26 items-center justify-center overflow-hidden rounded-md bg-gradient-to-b from-primary/90 to-primary px-3 text-xs font-semibold text-primary-foreground shadow-[0_0_1px_1px_rgba(255,255,255,0.08)_inset,0_1px_1.5px_0_rgba(0,0,0,0.32),0_0_0_0.5px_#1a94ff]\"\n    >\n      <AnimatePresence mode=\"popLayout\" initial={false}>\n        <motion.span\n          key={`${loading}`}\n          initial={{ opacity: 0, y: -25 }}\n          animate={{ opacity: 1, y: 0 }}\n          exit={{ opacity: 0, y: 25 }}\n          transition={{\n            type: \"spring\",\n            duration: 0.3,\n            bounce: 0,\n          }}\n          className=\"flex w-full items-center justify-center\"\n        >\n          {loading ? (\n            <Loader className=\"animate-spin size-3\" />\n          ) : (\n            <span>{text}</span>\n          )}\n        </motion.span>\n      </AnimatePresence>\n    </button>\n  )\n}\n\nconst useClickOutside = (\n  ref: RefObject<HTMLElement>,\n  handleOnClickOutside: (event: MouseEvent | TouchEvent) => void\n) => {\n  useEffect(() => {\n    const listener = (event: MouseEvent | TouchEvent) => {\n      if (!ref.current || ref.current.contains(event.target as Node)) {\n        return\n      }\n      handleOnClickOutside(event)\n    }\n    document.addEventListener(\"mousedown\", listener)\n    document.addEventListener(\"touchstart\", listener)\n    return () => {\n      document.removeEventListener(\"mousedown\", listener)\n      document.removeEventListener(\"touchstart\", listener)\n    }\n  }, [ref, handleOnClickOutside])\n}\n\nexport function PopoverFormSuccess({\n  title = \"Success\",\n  description = \"Thank you for your submission\",\n}) {\n  return (\n    <>\n      <svg\n        width=\"32\"\n        height=\"32\"\n        viewBox=\"0 0 32 32\"\n        fill=\"none\"\n        xmlns=\"http://www.w3.org/2000/svg\"\n        className=\"-mt-1\"\n      >\n        <path\n          d=\"M27.6 16C27.6 17.5234 27.3 19.0318 26.717 20.4392C26.1341 21.8465 25.2796 23.1253 24.2025 24.2025C23.1253 25.2796 21.8465 26.1341 20.4392 26.717C19.0318 27.3 17.5234 27.6 16 27.6C14.4767 27.6 12.9683 27.3 11.5609 26.717C10.1535 26.1341 8.87475 25.2796 7.79759 24.2025C6.72043 23.1253 5.86598 21.8465 5.28302 20.4392C4.70007 19.0318 4.40002 17.5234 4.40002 16C4.40002 12.9235 5.62216 9.97301 7.79759 7.79759C9.97301 5.62216 12.9235 4.40002 16 4.40002C19.0765 4.40002 22.027 5.62216 24.2025 7.79759C26.3779 9.97301 27.6 12.9235 27.6 16Z\"\n          fill=\"#2090FF\"\n          fillOpacity=\"0.16\"\n        />\n        <path\n          d=\"M12.1334 16.9667L15.0334 19.8667L19.8667 13.1M27.6 16C27.6 17.5234 27.3 19.0318 26.717 20.4392C26.1341 21.8465 25.2796 23.1253 24.2025 24.2025C23.1253 25.2796 21.8465 26.1341 20.4392 26.717C19.0318 27.3 17.5234 27.6 16 27.6C14.4767 27.6 12.9683 27.3 11.5609 26.717C10.1535 26.1341 8.87475 25.2796 7.79759 24.2025C6.72043 23.1253 5.86598 21.8465 5.28302 20.4392C4.70007 19.0318 4.40002 17.5234 4.40002 16C4.40002 12.9235 5.62216 9.97301 7.79759 7.79759C9.97301 5.62216 12.9235 4.40002 16 4.40002C19.0765 4.40002 22.027 5.62216 24.2025 7.79759C26.3779 9.97301 27.6 12.9235 27.6 16Z\"\n          stroke=\"#2090FF\"\n          strokeWidth=\"2.4\"\n          strokeLinecap=\"round\"\n          strokeLinejoin=\"round\"\n        />\n      </svg>\n      <h3 className=\"mb-1 mt-2 text-sm font-medium text-primary\">{title}</h3>\n      <p className=\"text-sm text-muted-foreground max-w-xs text-pretty mx-auto text-center\">\n        {description}\n      </p>\n    </>\n  )\n}\n\nexport function PopoverFormSeparator({\n  width = 352,\n  height = 2,\n}: {\n  width?: number | string\n  height?: number\n}) {\n  return (\n    <svg\n      className=\"absolute left-0 right-0 top-[-1px]\"\n      width={width}\n      height={height}\n      viewBox=\"0 0 352 2\"\n      fill=\"none\"\n      xmlns=\"http://www.w3.org/2000/svg\"\n    >\n      <path d=\"M0 1H352\" className=\"stroke-border\" strokeDasharray=\"4 4\" />\n    </svg>\n  )\n}\n\nfunction PopoverFormCutOutTopIcon({\n  width = 44,\n  height = 30,\n}: {\n  width?: number\n  height?: number\n}) {\n  const aspectRatio = 6 / 12\n  const calculatedHeight = width * aspectRatio\n  const calculatedWidth = height / aspectRatio\n\n  const finalWidth = Math.min(width, calculatedWidth)\n  const finalHeight = Math.min(height, calculatedHeight)\n\n  return (\n    <svg\n      width={finalWidth}\n      height={finalHeight}\n      viewBox=\"0 0 6 12\"\n      fill=\"none\"\n      xmlns=\"http://www.w3.org/2000/svg\"\n      className=\"rotate-90 mt-[1px]\"\n      preserveAspectRatio=\"none\"\n    >\n      <g clipPath=\"url(#clip0_2029_22)\">\n        <path\n          d=\"M0 2C0.656613 2 1.30679 2.10346 1.91341 2.30448C2.52005 2.5055 3.07124 2.80014 3.53554 3.17157C3.99982 3.54301 4.36812 3.98396 4.6194 4.46927C4.87067 4.95457 5 5.47471 5 6C5 6.52529 4.87067 7.04543 4.6194 7.53073C4.36812 8.01604 3.99982 8.45699 3.53554 8.82843C3.07124 9.19986 2.52005 9.4945 1.91341 9.69552C1.30679 9.89654 0.656613 10 0 10V6V2Z\"\n          className=\"fill-muted\"\n        />\n        <path\n          d=\"M1 12V10C2.06087 10 3.07828 9.57857 3.82843 8.82843C4.57857 8.07828 5 7.06087 5 6C5 4.93913 4.57857 3.92172 3.82843 3.17157C3.07828 2.42143 2.06087 2 1 2V0\"\n          className=\"stroke-border\"\n          strokeWidth={0.6}\n          strokeLinejoin=\"round\"\n        />\n      </g>\n      <defs>\n        <clipPath id=\"clip0_2029_22\">\n          <rect width={finalWidth} height={finalHeight} fill=\"white\" />\n        </clipPath>\n      </defs>\n    </svg>\n  )\n}\n\nexport function PopoverFormCutOutLeftIcon() {\n  return (\n    <svg\n      width=\"6\"\n      height=\"12\"\n      viewBox=\"0 0 6 12\"\n      fill=\"none\"\n      xmlns=\"http://www.w3.org/2000/svg\"\n    >\n      <g clipPath=\"url(#clip0_2029_22)\">\n        <path\n          d=\"M0 2C0.656613 2 1.30679 2.10346 1.91341 2.30448C2.52005 2.5055 3.07124 2.80014 3.53554 3.17157C3.99982 3.54301 4.36812 3.98396 4.6194 4.46927C4.87067 4.95457 5 5.47471 5 6C5 6.52529 4.87067 7.04543 4.6194 7.53073C4.36812 8.01604 3.99982 8.45699 3.53554 8.82843C3.07124 9.19986 2.52005 9.4945 1.91341 9.69552C1.30679 9.89654 0.656613 10 0 10V6V2Z\"\n          className=\"fill-muted\"\n        />\n        <path\n          d=\"M1 12V10C2.06087 10 3.07828 9.57857 3.82843 8.82843C4.57857 8.07828 5 7.06087 5 6C5 4.93913 4.57857 3.92172 3.82843 3.17157C3.07828 2.42143 2.06087 2 1 2V0\"\n          className=\"stroke-border\"\n          strokeWidth=\"1\"\n          strokeLinejoin=\"round\"\n        />\n      </g>\n      <defs>\n        <clipPath id=\"clip0_2029_22\">\n          <rect width=\"6\" height=\"12\" fill=\"white\" />\n        </clipPath>\n      </defs>\n    </svg>\n  )\n}\n\nexport function PopoverFormCutOutRightIcon() {\n  return (\n    <svg\n      width=\"6\"\n      height=\"12\"\n      viewBox=\"0 0 6 12\"\n      fill=\"none\"\n      xmlns=\"http://www.w3.org/2000/svg\"\n    >\n      <g clipPath=\"url(#clip0_2029_22)\">\n        <path\n          d=\"M0 2C0.656613 2 1.30679 2.10346 1.91341 2.30448C2.52005 2.5055 3.07124 2.80014 3.53554 3.17157C3.99982 3.54301 4.36812 3.98396 4.6194 4.46927C4.87067 4.95457 5 5.47471 5 6C5 6.52529 4.87067 7.04543 4.6194 7.53073C4.36812 8.01604 3.99982 8.45699 3.53554 8.82843C3.07124 9.19986 2.52005 9.4945 1.91341 9.69552C1.30679 9.89654 0.656613 10 0 10V6V2Z\"\n          className=\"fill-muted\"\n        />\n        <path\n          d=\"M1 12V10C2.06087 10 3.07828 9.57857 3.82843 8.82843C4.57857 8.07828 5 7.06087 5 6C5 4.93913 4.57857 3.92172 3.82843 3.17157C3.07828 2.42143 2.06087 2 1 2V0\"\n          className=\"stroke-border\"\n          strokeWidth=\"1\"\n          strokeLinejoin=\"round\"\n        />\n      </g>\n      <defs>\n        <clipPath id=\"clip0_2029_22\">\n          <rect width=\"6\" height=\"12\" fill=\"white\" />\n        </clipPath>\n      </defs>\n    </svg>\n  )\n}\n\nexport default PopoverForm\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;AACA;AAAA;AACA;AAAA;AAJA;;;;;AAmBO,SAAS,YAAY,EAC1B,IAAI,EACJ,OAAO,EACP,SAAS,EACT,WAAW,EACX,YAAY,EACZ,QAAQ,OAAO,EACf,SAAS,OAAO,EAChB,QAAQ,UAAU,EAClB,kBAAkB,KAAK,EACN;IACjB,MAAM,MAAM,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IACnC,gBAAgB,KAAK,IAAM,QAAQ;IAEnC,qBACE,8OAAC;QAEC,WAAU;;0BAEV,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gBACZ,UAAU,GAAG,MAAM,QAAQ,CAAC;gBAC5B,SAAS,IAAM,QAAQ;gBACvB,OAAO;oBAAE,cAAc;gBAAE;gBACzB,WAAU;0BAEV,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;oBAAC,UAAU,GAAG,MAAM,MAAM,CAAC;8BAAG;;;;;;;;;;;0BAE5C,8OAAC,yLAAA,CAAA,kBAAe;0BACb,sBACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,UAAU,GAAG,MAAM,QAAQ,CAAC;oBAC5B,WAAU;oBACV,KAAK;oBACL,OAAO;wBAAE,cAAc;wBAAI;wBAAO;oBAAO;;sCAEzC,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;4BACV,aAAW;4BACX,WAAU;4BACV,UAAU,GAAG,MAAM,MAAM,CAAC;4BAC1B,gBAAc;sCAEb;;;;;;wBAGF,iCACC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,SAAS,IAAM,QAAQ;oCACvB,WAAU;oCACV,cAAW;8CAEX,cAAA,8OAAC,gNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;8CAGvB,8OAAC;;;;;;;;;;;sCAIL,8OAAC,yLAAA,CAAA,kBAAe;4BAAC,MAAK;sCACnB,4BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,GAAG,CAAC;oCAAI,SAAS;oCAAG,QAAQ;gCAAY;gCACnD,SAAS;oCAAE,GAAG;oCAAG,SAAS;oCAAG,QAAQ;gCAAY;gCACjD,YAAY;oCAAE,MAAM;oCAAU,UAAU;oCAAK,QAAQ;gCAAE;gCACvD,WAAU;0CAET,8BAAgB,8OAAC;;;;;+BANd;;;;qDASN,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,MAAM;oCAAE,GAAG;oCAAG,SAAS;oCAAG,QAAQ;gCAAY;gCAC9C,YAAY;oCAAE,MAAM;oCAAU,UAAU;oCAAK,QAAQ;gCAAE;gCAEvD,OAAO;oCAAE,cAAc;gCAAG;gCAC1B,WAAU;0CAET;+BAJG;;;;;;;;;;;;;;;;;;;;;;OAzDX;;;;;AAsEX;AAEO,SAAS,kBAAkB,EAChC,OAAO,EACP,OAAO,QAAQ,EAIhB;IACC,qBACE,8OAAC;QACC,MAAK;QACL,WAAU;kBAEV,cAAA,8OAAC,yLAAA,CAAA,kBAAe;YAAC,MAAK;YAAY,SAAS;sBACzC,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;gBAEV,SAAS;oBAAE,SAAS;oBAAG,GAAG,CAAC;gBAAG;gBAC9B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,MAAM;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC1B,YAAY;oBACV,MAAM;oBACN,UAAU;oBACV,QAAQ;gBACV;gBACA,WAAU;0BAET,wBACC,8OAAC,sMAAA,CAAA,SAAM;oBAAC,WAAU;;;;;yCAElB,8OAAC;8BAAM;;;;;;eAdJ,GAAG,SAAS;;;;;;;;;;;;;;;AAoB3B;AAEA,MAAM,kBAAkB,CACtB,KACA;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW,CAAC;YAChB,IAAI,CAAC,IAAI,OAAO,IAAI,IAAI,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;gBAC9D;YACF;YACA,qBAAqB;QACvB;QACA,SAAS,gBAAgB,CAAC,aAAa;QACvC,SAAS,gBAAgB,CAAC,cAAc;QACxC,OAAO;YACL,SAAS,mBAAmB,CAAC,aAAa;YAC1C,SAAS,mBAAmB,CAAC,cAAc;QAC7C;IACF,GAAG;QAAC;QAAK;KAAqB;AAChC;AAEO,SAAS,mBAAmB,EACjC,QAAQ,SAAS,EACjB,cAAc,+BAA+B,EAC9C;IACC,qBACE;;0BACE,8OAAC;gBACC,OAAM;gBACN,QAAO;gBACP,SAAQ;gBACR,MAAK;gBACL,OAAM;gBACN,WAAU;;kCAEV,8OAAC;wBACC,GAAE;wBACF,MAAK;wBACL,aAAY;;;;;;kCAEd,8OAAC;wBACC,GAAE;wBACF,QAAO;wBACP,aAAY;wBACZ,eAAc;wBACd,gBAAe;;;;;;;;;;;;0BAGnB,8OAAC;gBAAG,WAAU;0BAA8C;;;;;;0BAC5D,8OAAC;gBAAE,WAAU;0BACV;;;;;;;;AAIT;AAEO,SAAS,qBAAqB,EACnC,QAAQ,GAAG,EACX,SAAS,CAAC,EAIX;IACC,qBACE,8OAAC;QACC,WAAU;QACV,OAAO;QACP,QAAQ;QACR,SAAQ;QACR,MAAK;QACL,OAAM;kBAEN,cAAA,8OAAC;YAAK,GAAE;YAAW,WAAU;YAAgB,iBAAgB;;;;;;;;;;;AAGnE;AAEA,SAAS,yBAAyB,EAChC,QAAQ,EAAE,EACV,SAAS,EAAE,EAIZ;IACC,MAAM,cAAc,IAAI;IACxB,MAAM,mBAAmB,QAAQ;IACjC,MAAM,kBAAkB,SAAS;IAEjC,MAAM,aAAa,KAAK,GAAG,CAAC,OAAO;IACnC,MAAM,cAAc,KAAK,GAAG,CAAC,QAAQ;IAErC,qBACE,8OAAC;QACC,OAAO;QACP,QAAQ;QACR,SAAQ;QACR,MAAK;QACL,OAAM;QACN,WAAU;QACV,qBAAoB;;0BAEpB,8OAAC;gBAAE,UAAS;;kCACV,8OAAC;wBACC,GAAE;wBACF,WAAU;;;;;;kCAEZ,8OAAC;wBACC,GAAE;wBACF,WAAU;wBACV,aAAa;wBACb,gBAAe;;;;;;;;;;;;0BAGnB,8OAAC;0BACC,cAAA,8OAAC;oBAAS,IAAG;8BACX,cAAA,8OAAC;wBAAK,OAAO;wBAAY,QAAQ;wBAAa,MAAK;;;;;;;;;;;;;;;;;;;;;;AAK7D;AAEO,SAAS;IACd,qBACE,8OAAC;QACC,OAAM;QACN,QAAO;QACP,SAAQ;QACR,MAAK;QACL,OAAM;;0BAEN,8OAAC;gBAAE,UAAS;;kCACV,8OAAC;wBACC,GAAE;wBACF,WAAU;;;;;;kCAEZ,8OAAC;wBACC,GAAE;wBACF,WAAU;wBACV,aAAY;wBACZ,gBAAe;;;;;;;;;;;;0BAGnB,8OAAC;0BACC,cAAA,8OAAC;oBAAS,IAAG;8BACX,cAAA,8OAAC;wBAAK,OAAM;wBAAI,QAAO;wBAAK,MAAK;;;;;;;;;;;;;;;;;;;;;;AAK3C;AAEO,SAAS;IACd,qBACE,8OAAC;QACC,OAAM;QACN,QAAO;QACP,SAAQ;QACR,MAAK;QACL,OAAM;;0BAEN,8OAAC;gBAAE,UAAS;;kCACV,8OAAC;wBACC,GAAE;wBACF,WAAU;;;;;;kCAEZ,8OAAC;wBACC,GAAE;wBACF,WAAU;wBACV,aAAY;wBACZ,gBAAe;;;;;;;;;;;;0BAGnB,8OAAC;0BACC,cAAA,8OAAC;oBAAS,IAAG;8BACX,cAAA,8OAAC;wBAAK,OAAM;wBAAI,QAAO;wBAAK,MAAK;;;;;;;;;;;;;;;;;;;;;;AAK3C;uCAEe", "debugId": null}}, {"offset": {"line": 1240, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Development/My%20Projects/Portfolio/my-app/src/components/ui/contactMeButton.tsx"], "sourcesContent": ["\"use client\";\r\nimport React, { useState, useEffect } from \"react\";\r\n\r\ntype FormState = \"idle\" | \"loading\" | \"success\";\r\n\r\nimport {\r\n  PopoverForm,\r\n  PopoverFormButton,\r\n  PopoverFormCutOutLeftIcon,\r\n  PopoverFormCutOutRightIcon,\r\n  PopoverFormSeparator,\r\n  PopoverFormSuccess,\r\n} from \"./popover-form\";\r\n\r\nexport default function ContactMeButton() {\r\n  const [formState, setFormState] = useState<FormState>(\"idle\");\r\n  const [open, setOpen] = useState(false);\r\n  const [feedback, setFeedback] = useState(\"\");\r\n\r\n  function submit() {\r\n    setFormState(\"loading\");\r\n    setTimeout(() => {\r\n      setFormState(\"success\");\r\n    }, 1500);\r\n\r\n    setTimeout(() => {\r\n      setOpen(false);\r\n      setFormState(\"idle\");\r\n      setFeedback(\"\");\r\n    }, 3300);\r\n  }\r\n\r\n  useEffect(() => {\r\n    const handleKeyDown = (event: KeyboardEvent) => {\r\n      if (event.key === \"Escape\") {\r\n        setOpen(false);\r\n      }\r\n\r\n      if (\r\n        (event.ctrlKey || event.metaKey) &&\r\n        event.key === \"Enter\" &&\r\n        open &&\r\n        formState === \"idle\"\r\n      ) {\r\n        submit();\r\n      }\r\n    };\r\n\r\n    window.addEventListener(\"keydown\", handleKeyDown);\r\n    return () => window.removeEventListener(\"keydown\", handleKeyDown);\r\n  }, [open, formState]);\r\n\r\n  return (\r\n    <>\r\n      {/* Simple button for navbar */}\r\n      <button\r\n        onClick={() => setOpen(true)}\r\n        className=\"px-3 py-1 text-sm font-medium bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors\"\r\n      >\r\n        Contact Me\r\n      </button>\r\n\r\n      {/* Popup form */}\r\n      {open && (\r\n        <PopoverForm\r\n          title=\"Contact Me\"\r\n          open={open}\r\n          setOpen={setOpen}\r\n          width=\"364px\"\r\n          height=\"192px\"\r\n          showCloseButton={formState !== \"success\"}\r\n          showSuccess={formState === \"success\"}\r\n          openChild={\r\n            <form\r\n              onSubmit={(e) => {\r\n                e.preventDefault();\r\n                if (!feedback) return;\r\n                submit();\r\n              }}\r\n            >\r\n              <div className=\"relative\">\r\n                <textarea\r\n                  autoFocus\r\n                  placeholder=\"Your message...\"\r\n                  value={feedback}\r\n                  onChange={(e) => setFeedback(e.target.value)}\r\n                  className=\"h-32 w-full resize-none rounded-t-lg p-3 text-sm outline-none\"\r\n                  required\r\n                />\r\n              </div>\r\n              <div className=\"relative flex h-12 items-center px-[10px]\">\r\n                <PopoverFormSeparator />\r\n                <div className=\"absolute left-0 top-0 -translate-x-[1.5px] -translate-y-1/2\">\r\n                  <PopoverFormCutOutLeftIcon />\r\n                </div>\r\n                <div className=\"absolute right-0 top-0 translate-x-[1.5px] -translate-y-1/2 rotate-180\">\r\n                  <PopoverFormCutOutRightIcon />\r\n                </div>\r\n                <PopoverFormButton\r\n                  loading={formState === \"loading\"}\r\n                  text=\"Send\"\r\n                />\r\n              </div>\r\n            </form>\r\n          }\r\n          successChild={\r\n            <PopoverFormSuccess\r\n              title=\"Message Sent!\"\r\n              description=\"Thank you for reaching out! I'll get back to you soon.\"\r\n            />\r\n          }\r\n        />\r\n      )}\r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AACA;AAIA;AALA;;;;AAce,SAAS;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa;IACtD,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,SAAS;QACP,aAAa;QACb,WAAW;YACT,aAAa;QACf,GAAG;QAEH,WAAW;YACT,QAAQ;YACR,aAAa;YACb,YAAY;QACd,GAAG;IACL;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,gBAAgB,CAAC;YACrB,IAAI,MAAM,GAAG,KAAK,UAAU;gBAC1B,QAAQ;YACV;YAEA,IACE,CAAC,MAAM,OAAO,IAAI,MAAM,OAAO,KAC/B,MAAM,GAAG,KAAK,WACd,QACA,cAAc,QACd;gBACA;YACF;QACF;QAEA,OAAO,gBAAgB,CAAC,WAAW;QACnC,OAAO,IAAM,OAAO,mBAAmB,CAAC,WAAW;IACrD,GAAG;QAAC;QAAM;KAAU;IAEpB,qBACE;;0BAEE,8OAAC;gBACC,SAAS,IAAM,QAAQ;gBACvB,WAAU;0BACX;;;;;;YAKA,sBACC,8OAAC,2IAAA,CAAA,cAAW;gBACV,OAAM;gBACN,MAAM;gBACN,SAAS;gBACT,OAAM;gBACN,QAAO;gBACP,iBAAiB,cAAc;gBAC/B,aAAa,cAAc;gBAC3B,yBACE,8OAAC;oBACC,UAAU,CAAC;wBACT,EAAE,cAAc;wBAChB,IAAI,CAAC,UAAU;wBACf;oBACF;;sCAEA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,SAAS;gCACT,aAAY;gCACZ,OAAO;gCACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;gCAC3C,WAAU;gCACV,QAAQ;;;;;;;;;;;sCAGZ,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,2IAAA,CAAA,uBAAoB;;;;;8CACrB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,2IAAA,CAAA,4BAAyB;;;;;;;;;;8CAE5B,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,2IAAA,CAAA,6BAA0B;;;;;;;;;;8CAE7B,8OAAC,2IAAA,CAAA,oBAAiB;oCAChB,SAAS,cAAc;oCACvB,MAAK;;;;;;;;;;;;;;;;;;gBAKb,4BACE,8OAAC,2IAAA,CAAA,qBAAkB;oBACjB,OAAM;oBACN,aAAY;;;;;;;;;;;;;AAO1B", "debugId": null}}, {"offset": {"line": 1397, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Development/My%20Projects/Portfolio/my-app/src/components/ui/navbar.tsx"], "sourcesContent": ["\"use client\";\r\nimport React, { useState, useRef, useCallback } from \"react\";\r\nimport FlipLink from \"./text-effect-flipper\";\r\nimport { useGS<PERSON> } from \"@gsap/react\";\r\nimport gsap from \"gsap\";\r\nimport ThemeToggleButton from \"./theme-toggle-button\";\r\nimport ContactMeButton from \"./contactMeButton\";\r\n\r\nconst MENU_ITEMS = [\r\n  { href: \"/\", label: \"Home\" },\r\n  { href: \"#about\", label: \"About\" },\r\n  { href: \"#projects\", label: \"Projects\" },\r\n  { href: \"#skills\", label: \"Skills\" },\r\n];\r\n\r\nconst MenuButton = ({ isOpen, onClick }) => (\r\n  <button\r\n    onClick={onClick}\r\n    className=\"text-4xl hover:text-black-300 transition-colors\"\r\n    aria-label=\"Toggle menu\"\r\n  >\r\n    {isOpen ? \"×\" : \"☰\"}\r\n  </button>\r\n);\r\n\r\n// eslint-disable-next-line react/display-name\r\nconst MenuItem = React.forwardRef(({ href, label }, ref) => (\r\n  <li ref={ref} className=\"relative group\">\r\n    <a\r\n      className=\"text-6xl font-semibold inline-block\r\n             leading-normal py-4\r\n             transition-all duration-300 ease-in-out\r\n             text-black dark:text-white\r\n             hover:text-red-800 dark:hover:text-red-400 hover:scale-110\r\n             font-[skiper]\"\r\n    >\r\n      <FlipLink href={href}>{label}</FlipLink>\r\n    </a>\r\n    {/* Bottom border */}\r\n    <span\r\n      className=\"absolute left-0 bottom-0 w-0 h-[3px] bg-red-600 dark:bg-red-400\r\n                 transition-all duration-300 ease-in-out group-hover:w-full\"\r\n    ></span>\r\n  </li>\r\n));\r\n\r\nexport const Navbar = () => {\r\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\r\n  const [isMenuVisible, setIsMenuVisible] = useState(false); // Track visibility\r\n  const menuRef = useRef(null);\r\n  const menuItemsRef = useRef([]);\r\n\r\n  const openMenuAnimation = useCallback(() => {\r\n    gsap.fromTo(\r\n      menuRef.current,\r\n      { opacity: 0 },\r\n      { opacity: 1, duration: 0.5, ease: \"power2.inOut\" }\r\n    );\r\n\r\n    gsap.fromTo(\r\n      menuItemsRef.current,\r\n      { y: -50, opacity: 0 },\r\n      {\r\n        y: 0,\r\n        opacity: 1,\r\n        duration: 0.6,\r\n        stagger: 0.1,\r\n        ease: \"back.out(1.7)\",\r\n      }\r\n    );\r\n  }, []);\r\n\r\n  const closeMenuAnimation = useCallback(() => {\r\n    gsap.to(menuItemsRef.current, {\r\n      y: -50,\r\n      opacity: 0,\r\n      duration: 0.4,\r\n      stagger: 0.1,\r\n      ease: \"power2.in\",\r\n    });\r\n\r\n    gsap.to(menuRef.current, {\r\n      opacity: 0,\r\n      duration: 0.5,\r\n      ease: \"power2.inOut\",\r\n      delay: 0.3,\r\n      onComplete: () => {\r\n        setIsMenuVisible(false);\r\n      },\r\n    });\r\n  }, []);\r\n\r\n  const toggleMenu = useCallback(() => {\r\n    if (!isMenuOpen) {\r\n      setIsMenuVisible(true);\r\n      setIsMenuOpen(true);\r\n    } else {\r\n      setIsMenuOpen(false);\r\n      closeMenuAnimation();\r\n    }\r\n  }, [isMenuOpen, closeMenuAnimation]);\r\n\r\n  useGSAP(() => {\r\n    if (isMenuOpen) {\r\n      openMenuAnimation();\r\n    }\r\n  }, [isMenuOpen, openMenuAnimation]);\r\n\r\n  return (\r\n    <>\r\n      <div className=\"flex justify-between items-center p-4 fixed w-full top-0 z-50\">\r\n        <div className=\"text-2xl font-bold\">\r\n          <a href=\"/\">Portfolio</a>\r\n        </div>\r\n\r\n        <div className=\"flex items-center gap-4\">\r\n          <ContactMeButton />\r\n          <ThemeToggleButton />\r\n          <MenuButton isOpen={isMenuOpen} onClick={toggleMenu} />\r\n        </div>\r\n      </div>\r\n\r\n      {isMenuVisible && (\r\n        <div\r\n          ref={menuRef}\r\n          className=\"fixed inset-0 bg-white dark:bg-zinc-900 bg-opacity-95 dark:bg-opacity-95 z-40 flex items-center justify-center\"\r\n        >\r\n          <nav className=\"text-center\">\r\n            <ul className=\"flex flex-col gap-12\">\r\n              {MENU_ITEMS.map((item, index) => (\r\n                <MenuItem\r\n                  key={item.href}\r\n                  href={item.href}\r\n                  label={item.label}\r\n                  ref={(el) => (menuItemsRef.current[index] = el)}\r\n                />\r\n              ))}\r\n            </ul>\r\n          </nav>\r\n        </div>\r\n      )}\r\n    </>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AACA;AANA;;;;;;;;AAQA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAK,OAAO;IAAO;IAC3B;QAAE,MAAM;QAAU,OAAO;IAAQ;IACjC;QAAE,MAAM;QAAa,OAAO;IAAW;IACvC;QAAE,MAAM;QAAW,OAAO;IAAS;CACpC;AAED,MAAM,aAAa,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,iBACrC,8OAAC;QACC,SAAS;QACT,WAAU;QACV,cAAW;kBAEV,SAAS,MAAM;;;;;;AAIpB,8CAA8C;AAC9C,MAAM,yBAAW,qMAAA,CAAA,UAAK,CAAC,UAAU,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,oBAClD,8OAAC;QAAG,KAAK;QAAK,WAAU;;0BACtB,8OAAC;gBACC,WAAU;0BAOV,cAAA,8OAAC,qJAAA,CAAA,UAAQ;oBAAC,MAAM;8BAAO;;;;;;;;;;;0BAGzB,8OAAC;gBACC,WAAU;;;;;;;;;;;;AAMT,MAAM,SAAS;IACpB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ,mBAAmB;IAC9E,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACvB,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE,EAAE;IAE9B,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACpC,6IAAA,CAAA,UAAI,CAAC,MAAM,CACT,QAAQ,OAAO,EACf;YAAE,SAAS;QAAE,GACb;YAAE,SAAS;YAAG,UAAU;YAAK,MAAM;QAAe;QAGpD,6IAAA,CAAA,UAAI,CAAC,MAAM,CACT,aAAa,OAAO,EACpB;YAAE,GAAG,CAAC;YAAI,SAAS;QAAE,GACrB;YACE,GAAG;YACH,SAAS;YACT,UAAU;YACV,SAAS;YACT,MAAM;QACR;IAEJ,GAAG,EAAE;IAEL,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACrC,6IAAA,CAAA,UAAI,CAAC,EAAE,CAAC,aAAa,OAAO,EAAE;YAC5B,GAAG,CAAC;YACJ,SAAS;YACT,UAAU;YACV,SAAS;YACT,MAAM;QACR;QAEA,6IAAA,CAAA,UAAI,CAAC,EAAE,CAAC,QAAQ,OAAO,EAAE;YACvB,SAAS;YACT,UAAU;YACV,MAAM;YACN,OAAO;YACP,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF,GAAG,EAAE;IAEL,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC7B,IAAI,CAAC,YAAY;YACf,iBAAiB;YACjB,cAAc;QAChB,OAAO;YACL,cAAc;YACd;QACF;IACF,GAAG;QAAC;QAAY;KAAmB;IAEnC,CAAA,GAAA,+IAAA,CAAA,UAAO,AAAD,EAAE;QACN,IAAI,YAAY;YACd;QACF;IACF,GAAG;QAAC;QAAY;KAAkB;IAElC,qBACE;;0BACE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAE,MAAK;sCAAI;;;;;;;;;;;kCAGd,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,2IAAA,CAAA,UAAe;;;;;0CAChB,8OAAC,qJAAA,CAAA,UAAiB;;;;;0CAClB,8OAAC;gCAAW,QAAQ;gCAAY,SAAS;;;;;;;;;;;;;;;;;;YAI5C,+BACC,8OAAC;gBACC,KAAK;gBACL,WAAU;0BAEV,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAG,WAAU;kCACX,WAAW,GAAG,CAAC,CAAC,MAAM,sBACrB,8OAAC;gCAEC,MAAM,KAAK,IAAI;gCACf,OAAO,KAAK,KAAK;gCACjB,KAAK,CAAC,KAAQ,aAAa,OAAO,CAAC,MAAM,GAAG;+BAHvC,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;AAYhC", "debugId": null}}]}