{"version": 3, "sources": [], "sections": [{"offset": {"line": 13, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Development/My%20Projects/Portfolio/my-app/src/components/ui/theme-provider.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { ThemeProvider as NextThemesProvider } from \"next-themes\"\nimport { type ThemeProviderProps } from \"next-themes/dist/types\"\n\nexport function ThemeProvider({ children, ...props }: ThemeProviderProps) {\n  return <NextThemesProvider {...props}>{children}</NextThemesProvider>\n}\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAMO,SAAS,cAAc,EAAE,QAAQ,EAAE,GAAG,OAA2B;IACtE,qBAAO,8OAAC,gJAAA,CAAA,gBAAkB;QAAE,GAAG,KAAK;kBAAG;;;;;;AACzC", "debugId": null}}, {"offset": {"line": 35, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Development/My%20Projects/Portfolio/my-app/src/components/ui/preLoader.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect } from 'react';\r\nimport { motion, AnimatePresence } from 'framer-motion';\r\n\r\nconst words = [\r\n  \"Hello..!\", // English\r\n  \"こんにちは\", // Japanese\r\n  \"你好\", // Chinese\r\n  \"안녕하세요\", // Korean\r\n  \"नमस्ते\", // Hindi\r\n  \"مرحبا\", // Arabic\r\n  \"Hola\", // Spanish\r\n  \"Bonjour\", // French\r\n];\r\n\r\nexport default function Preloader() {\r\n  const [index, setIndex] = useState(0);\r\n  const [showLoader, setShowLoader] = useState(true);\r\n\r\n  useEffect(() => {\r\n    \r\n    const interval = setInterval(() => {\r\n      setIndex((prevIndex) => (prevIndex + 1) % words.length);\r\n    }, 600); \r\n\r\n    \r\n    const hideTimeout = setTimeout(() => {\r\n      setShowLoader(false);\r\n    }, 4000);\r\n\r\n    // Cleanup timers on component unmount\r\n    return () => {\r\n      clearInterval(interval);\r\n      clearTimeout(hideTimeout);\r\n    };\r\n  }, []);\r\n\r\n  // Variants for the text animation - slower transitions\r\n  const textVariants = {\r\n    initial: {\r\n      opacity: 0,\r\n      y: 20,\r\n    },\r\n    animate: {\r\n      opacity: 1,\r\n      y: 0,\r\n      transition: {\r\n        duration: 0.4,\r\n        ease: 'easeOut',\r\n      },\r\n    },\r\n    exit: {\r\n      opacity: 0,\r\n      y: -20,\r\n      transition: {\r\n        duration: 0.4,\r\n        ease: 'easeIn',\r\n      },\r\n    },\r\n  };\r\n  \r\n  // Variant for the loader container fade-out\r\n  const loaderVariants = {\r\n    exit: {\r\n      opacity: 0,\r\n      transition: {\r\n        duration: 0.8,\r\n        ease: 'easeInOut',\r\n      },\r\n    },\r\n  };\r\n\r\n  return (\r\n    <AnimatePresence>\r\n      {showLoader && (\r\n        <motion.div\r\n          className=\"fixed inset-0 z-60 flex items-center justify-center bg-white dark:bg-black\"\r\n          variants={loaderVariants}\r\n          exit=\"exit\"\r\n        >\r\n          <div className=\"relative\">\r\n            <div className=\"absolute inset-0 -z-10 blur-3xl bg-gradient-to-r from-pink-500/30 via-purple-500/30 to-cyan-500/30\" />\r\n            <AnimatePresence mode=\"wait\">\r\n              <motion.h1\r\n                key={words[index]}\r\n                className=\"text-4xl font-serif text-gray-700 dark:text-gray-300 md:text-8xl\"\r\n                variants={textVariants}\r\n                initial=\"initial\"\r\n                animate=\"animate\"\r\n                exit=\"exit\"\r\n              >\r\n                {words[index]}\r\n              </motion.h1>\r\n            </AnimatePresence>\r\n          </div>\r\n        </motion.div>\r\n      )}\r\n    </AnimatePresence>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAHA;;;;AAKA,MAAM,QAAQ;IACZ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAEc,SAAS;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QAER,MAAM,WAAW,YAAY;YAC3B,SAAS,CAAC,YAAc,CAAC,YAAY,CAAC,IAAI,MAAM,MAAM;QACxD,GAAG;QAGH,MAAM,cAAc,WAAW;YAC7B,cAAc;QAChB,GAAG;QAEH,sCAAsC;QACtC,OAAO;YACL,cAAc;YACd,aAAa;QACf;IACF,GAAG,EAAE;IAEL,uDAAuD;IACvD,MAAM,eAAe;QACnB,SAAS;YACP,SAAS;YACT,GAAG;QACL;QACA,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBACV,UAAU;gBACV,MAAM;YACR;QACF;QACA,MAAM;YACJ,SAAS;YACT,GAAG,CAAC;YACJ,YAAY;gBACV,UAAU;gBACV,MAAM;YACR;QACF;IACF;IAEA,4CAA4C;IAC5C,MAAM,iBAAiB;QACrB,MAAM;YACJ,SAAS;YACT,YAAY;gBACV,UAAU;gBACV,MAAM;YACR;QACF;IACF;IAEA,qBACE,8OAAC,yLAAA,CAAA,kBAAe;kBACb,4BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YACT,WAAU;YACV,UAAU;YACV,MAAK;sBAEL,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC,yLAAA,CAAA,kBAAe;wBAAC,MAAK;kCACpB,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;4BAER,WAAU;4BACV,UAAU;4BACV,SAAQ;4BACR,SAAQ;4BACR,MAAK;sCAEJ,KAAK,CAAC,MAAM;2BAPR,KAAK,CAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;AAejC", "debugId": null}}]}